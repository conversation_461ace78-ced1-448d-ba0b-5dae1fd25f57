import { useMemo } from "react";
import { Breadcrumb } from "antd";
import { Link, matchPath, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";

import { LINK } from "@link";
import { CONSTANT, WORKSPACE_TYPE } from "@constant";
import BREADCRUMB from "@src/constants/breadcrumb";

import DOT from "@src/asset/icon/dot/dot.svg";


const SEPARATOR = <img src={DOT} alt=""/>;

function HeaderBreadcrumb({ breadcrumbData, availableWorkspaces, ...props }) {
  const { t } = useTranslation();
  const { pathname } = useLocation();
  
  const breadcrumbItems = useMemo(() => {
    const matchProject = matchPath(LINK.PROJECT_DETAIL.format(":id"), pathname);
    
    const breadcrumbList = [];
    BREADCRUMB.forEach(breadcrumb => {
      if (matchPath(breadcrumb.path, pathname)) {
        breadcrumb.items.forEach(item => {
          let url, title;
          switch (item.urlType) {
            case CONSTANT.FOLDER_ID:
              if (breadcrumbData?.folder) {
                url = item.url.format(breadcrumbData.folder._id);
                title = item.lang ? t(item.lang) + `: ${breadcrumbData.folder.folderName}` : breadcrumbData.folder.folderName;
              }
              break;
            case CONSTANT.PROJECT_ID:
              url = item.url.format(matchProject.params.id);
              if (breadcrumbData?.project) {
                url = item.url.format(breadcrumbData.project._id);
                title = item.lang ? t(item.lang) + `: ${breadcrumbData.project.projectName}` : breadcrumbData.project.projectName;
              }
              break;
            case CONSTANT.WORKSPACE_NAME:
              url = item.url;
              if (availableWorkspaces) {
                const organizationWorkspace = availableWorkspaces?.find(item => item.type === WORKSPACE_TYPE.ORGANIZATIONAL);
                title = t("ORG_WORKSPACE_NAME").format(organizationWorkspace.organizationId?.name);
              }
              break;
            default:
              url = item.url;
              title = t(item.lang);
              break;
          }
          
          if (title) {
            breadcrumbList.push({ title: url ? <Link to={url}>{title}</Link> : title });
          }
        });
      }
    });
    
    
    return breadcrumbList;
  }, [t, pathname, breadcrumbData, availableWorkspaces]);
  
  return <div className="header-breadcrumb">
    <Breadcrumb
      separator={SEPARATOR}
      items={[
        { type: "separator", separator: SEPARATOR },
        ...breadcrumbItems,
      ]}
    />
  </div>;
}

function mapStateToProps(store) {
  const { breadcrumbData } = store.app;
  const { availableWorkspaces } = store.workspace;
  return { breadcrumbData, availableWorkspaces };
}

export default connect(mapStateToProps)(HeaderBreadcrumb);