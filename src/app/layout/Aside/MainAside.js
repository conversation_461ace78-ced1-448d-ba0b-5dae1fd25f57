import { connect } from "react-redux";
import { Collapse } from "antd";
import { useTranslation } from "react-i18next";
import clsx from "clsx";

import AsideItem from "@app/layout/Aside/AsideItem";

import { LINK } from "@link";
import { CONSTANT, WORKSPACE_TYPE } from "@constant";
import { API } from "@api";

import CHERVON_DOWN from "@src/asset/icon/chevron/chevron-down.svg";

import HOMEPAGE from "@src/asset/aside/homepage.svg";
import HOMEPAGE_ACTIVE from "@src/asset/aside/homepage-active.svg";

import DASHBOARD from "@src/asset/aside/dashboard.svg";
import DASHBOARD_ACTIVE from "@src/asset/aside/dashboard-active.svg";

import WELCOME from "@src/asset/aside/welcome.svg";
import WELCOME_ACTIVE from "@src/asset/aside/welcome-active.svg";

import TOOL from "@src/asset/aside/tool.svg";
import TOOL_ACTIVE from "@src/asset/aside/tool-active.svg";

import TEMPLATE from "@src/asset/aside/template.svg";
import TEMPLATE_ACTIVE from "@src/asset/aside/template-active.svg";

import RESOURCE from "@src/asset/aside/resource.svg";
import RESOURCE_ACTIVE from "@src/asset/aside/resource-active.svg";

import WORKSPACE from "@src/asset/aside/workspace.svg";
import WORKSPACE_ACTIVE from "@src/asset/aside/workspace-active.svg";

import STARRED from "@src/asset/aside/starred.svg";
import STARRED_ACTIVE from "@src/asset/aside/starred-active.svg";

import SHARE_WITH_ME from "@src/asset/aside/share-with-me.svg";
import SHARE_WITH_ME_ACTIVE from "@src/asset/aside/share-with-me-active.svg";

import BUILDING_ICON from "@src/asset/icon/building/building.svg";

import CREATE_EXAM from "@src/asset/aside/create-exam.svg";
import CREATE_EXAM_ACTIVE from "@src/asset/aside/create-exam-active.svg";

import MARK_EXAM from "@src/asset/aside/mark-exam.svg";
import MARK_EXAM_ACTIVE from "@src/asset/aside/mark-exam-active.svg";

function MainAside({ availableWorkspaces, user }) {
  const { t } = useTranslation();
  
  const organizationWorkspace = availableWorkspaces?.find(item => item.type === WORKSPACE_TYPE.ORGANIZATIONAL);
  
  const collapseProps = {
    defaultActiveKey: ["1"],
    expandIconPosition: "end",
    ghost: true,
    className: "aside-collapse",
    expandIcon: ({ isActive }) => <img src={CHERVON_DOWN} alt="" className={clsx({ "chevron-up": !isActive })} />,
  };
  
  const orgThumbnailFileId = user?.organizationId?.avatarId?.thumbnailFileId;
  const orgAvatarSrc = orgThumbnailFileId ? API.STREAM_ID.format(orgThumbnailFileId) : BUILDING_ICON;
  
  return <div className="aside-body">
    <AsideItem
      linkTo={LINK.WELCOME}
      title={t("WELCOME")}
      img={WELCOME}
      imgActive={WELCOME_ACTIVE}
    />
    <AsideItem
      linkTo={LINK.TOOLS}
      title={t("TOOL")}
      img={TOOL}
      imgActive={TOOL_ACTIVE}
    />
    <AsideItem
      linkTo={LINK.TEMPLATE}
      title={t("TEMPLATE_MENU_LABEL")}
      img={TEMPLATE}
      imgActive={TEMPLATE_ACTIVE}
    />
    
    <div className="aside-divider" />
    
    <AsideItem
      linkTo={LINK.CREATE_EXAM}
      title={t("CREATE_EXAM")}
      img={CREATE_EXAM}
      imgActive={CREATE_EXAM_ACTIVE}
    />
    <AsideItem
      linkTo={LINK.MARK_EXAM}
      title={t("MARK")}
      img={MARK_EXAM}
      imgActive={MARK_EXAM_ACTIVE}
    />
    
    <div className="aside-divider" />
    
    <Collapse
      items={[
        {
          key: "1",
          label: user?.fullName.toUpperCase(),
          children: <>
            <AsideItem
              linkTo={LINK.HOMEPAGE}
              title={t("MY_HOMEPAGE")}
              img={HOMEPAGE}
              imgActive={HOMEPAGE_ACTIVE}
            />
            <AsideItem
              linkTo={LINK.MY_WORKSPACE}
              title={t("MY_WORKSPACE")}
              img={WORKSPACE}
              imgActive={WORKSPACE_ACTIVE}
            />
            <AsideItem
              linkTo={LINK.STARRED}
              title={t("MY_STARRED")}
              img={STARRED}
              imgActive={STARRED_ACTIVE}
            />
            <AsideItem
              linkTo={LINK.SHARE_WITH_ME}
              title={t("SHARE_WITH_ME")}
              img={SHARE_WITH_ME}
              imgActive={SHARE_WITH_ME_ACTIVE}
            />
            <AsideItem
              linkTo={LINK.MY_DASHBOARD}
              title={t("MY_DASHBOARD")}
              img={DASHBOARD}
              imgActive={DASHBOARD_ACTIVE}
            />
            <AsideItem
              linkTo={LINK.RESOURCE}
              title={t("MY_RESOURCE")}
              img={RESOURCE}
              imgActive={RESOURCE_ACTIVE}
              state={{ asideType: CONSTANT.MY_RESOURCE }}
            />
          </>,
        },
      ]}
      {...collapseProps}
    />
    
    {user?.organizationId && <>
      <div className="aside-divider" />
      
      <Collapse
        items={[{
          key: "1",
          className: "organization-collapse",
          label: <>
            <span className={clsx("organization-avatar", { "default-avatar": !orgThumbnailFileId })}>
              <img src={orgAvatarSrc} alt="" />
            </span>
            <span className="organization-name">{user?.organizationId?.name.toUpperCase()}</span>
          </>,
          children: <>
            {organizationWorkspace && <AsideItem
              linkTo={LINK.ORGANIZATION_WORKSPACE}
              title={t("WORKSPACE").format(user?.organizationId?.name)}
              img={WORKSPACE}
              imgActive={WORKSPACE_ACTIVE}
            />}
            {user?.role === CONSTANT.ADMIN && <AsideItem
              linkTo={LINK.ORG_DASHBOARD}
              title={t("DASHBOARD").format(user?.organizationId?.name)}
              img={DASHBOARD}
              imgActive={DASHBOARD_ACTIVE}
            />}
            <AsideItem
              linkTo={LINK.RESOURCE}
              title={t("RESOURCE").format(user?.organizationId?.name)}
              img={RESOURCE}
              imgActive={RESOURCE_ACTIVE}
              state={{ asideType: CONSTANT.ORG_RESOURCE }}
            />
          </>,
        }]}
        {...collapseProps}
      />
    </>}
  </div>;
}

function mapStateToProps(store) {
  const { availableWorkspaces } = store.workspace;
  const { user } = store.auth;
  return { availableWorkspaces, user };
}

export default connect(mapStateToProps)(MainAside);