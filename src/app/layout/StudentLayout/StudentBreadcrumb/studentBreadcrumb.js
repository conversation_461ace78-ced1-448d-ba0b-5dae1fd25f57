import {<PERSON>IN<PERSON>} from '@link';
import {CONSTANT} from '@constant';

import '@src/common/prototype';

const STUDENT_BREADCRUMB = [
  // {
  //   path: LINK.WRITING,
  //   items: [
  //     { lang: "STUDY_HUB", url: LINK.WRITING },
  //     { lang: "WRITING", url: LINK.WRITING },
  //   ],
  // },
  {
    path: LINK.SPEAKING_SPEECHES,
    items: [{lang: 'STUDY_HUB', url: LINK.WRITING}, {lang: 'SPEAKING_MENU', url: LINK.SPEAKING}, {lang: 'MY_SPEECHES'}],
  },
  {
    path: LINK.WRITING_ESSAYS,
    items: [{lang: 'STUDY_HUB', url: LINK.WRITING}, {lang: 'WRITING_MENU', url: LINK.WRITING}, {lang: 'MY_ESSAYS'}],
  },
  {
    path: LINK.WRITING_RESULT.format(':id'),
    items: [{lang: 'STUDY_HUB', url: LINK.WRITING}, {lang: 'WRITING_MENU', url: LINK.WRITING}, {lang: 'RESULT'}],
  },
  {
    path: LINK.SPEAKING_ID.format(':id'),
    items: [{lang: 'STUDY_HUB', url: LINK.WRITING}, {lang: 'SPEAKING_MENU', url: LINK.SPEAKING}, {lang: 'RESULT'}],
  },
  {
    path: LINK.DICTATION.format(':id'),
    items: [
      {lang: 'STUDY_HUB', url: LINK.WRITING},
      {lang: 'DICTATION_SHADOWING', url: LINK.DICTATION_SHADOWING},
      {lang: 'DICTATION'},
    ],
  },
  {
    path: LINK.SHADOWING.format(':id'),
    items: [
      {lang: 'STUDY_HUB', url: LINK.WRITING},
      {lang: 'DICTATION_SHADOWING', url: LINK.DICTATION_SHADOWING},
      {lang: 'SHADOWING'},
    ],
  },
];

export default STUDENT_BREADCRUMB;
