import { API } from "@api";
import { createBase, deleteBase, getAllBase, getAllPaginationBase, updateBase } from "@services/Base";
export function getOutputTypes(query, loading) {
  return getAllBase(API.OUTPUT_TYPE, query);
}
export function getAllOutputTypes(paging, query) {
  query.sort = query.sort || "-createdAt";
  return getAllPaginationBase(API.OUTPUT_TYPE, paging, query, ["name", "code"], null);
}

export function createOutputType(data, toastError = false) {
  return createBase(API.OUTPUT_TYPE, data, [], false, toastError);
}

export function updateOutputType(data, toastError = false) {
  return updateBase(API.OUTPUT_TYPE_ID, data, [], false, toastError);
}

export function deleteOutputType(id, toastError = false) {
  return deleteBase(API.OUTPUT_TYPE_ID, id, false, toastError);
}
