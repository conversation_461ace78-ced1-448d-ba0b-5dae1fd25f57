import { API } from "@api";
import axios from "axios";
import fileDownload from "js-file-download";
import { createBase, getBase } from "@services/Base";
import { genPopulateParam, getFileExtension } from "@common/functionCommons";
import { convertSnakeCaseToCamelCase } from "@common/dataConverter";

export function getFileContent(project, loading = false) {
  const { _id, projectName } = project;
  let config = {
    responseType: "blob",
    loading,
  };
  return axios
    .get(`${API.REPORT}/${_id}/plaintext`, config)
    .then(res => {
      if (res.data) {
        fileDownload(res.data, `${projectName}.docx`);
      }
      return null;
    }).catch((err) => {
      console.log(err);
      return null;
    });
}

export function createProjectFile(data, config) {
  return axios.post(API.CREATE_PROJECT_FILE, (data), config)
              .then(response => {
                if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
                return null;
              })
              .catch((err) => {
                return null;
              });
}

export function createOneExam(data, config) {
  return axios.post(API.CREATE_ONE_EXAM, (data), config)
              .then(response => {
                if (response?.data) return response.data;
                return null;
              })
              .catch((err) => {
                return null;
              });
}

export function createMultiExam(data, config) {
  return axios.post(API.CREATE_MULTI_EXAM, (data), config)
              .then(response => {
                if (response?.data) return response.data;
                return null;
              })
              .catch((err) => {
                return null;
              });
}

export async function createOneMarkTest(data, config) {
  return axios.post(API.CREATE_ONE_MARK_TEST, (data), config)
              .then(response => {
                if (response?.data) return response.data;
                return null;
              })
              .catch((err) => {
                return null;
              });
}

export async function createMultiMarkTests(data, config) {
  return axios.post(API.CREATE_MULTI_MARK_TESTS, (data), config)
              .then(response => {
                if (response?.data) return response.data;
                return null;
              })
              .catch((err) => {
                return null;
              });
}

export function previewProjectFile(fileName) {
  const config = { responseType: "arraybuffer" };
  return axios.get(API.DOWNLOAD_REPORT_FILE.format(fileName), config)
              .then(response => {
                if (response.status === 200) {
                  const newBlob = new Blob([response.data], { type: "pdf" });
                  return URL.createObjectURL(newBlob);
                }
                return null;
              })
              .catch(() => null);
}


export function downloadResponse(content, responseId, loading = false) {
  let config = {
    responseType: "blob",
    loading,
  };
  
  return axios
    .get(`${API.REPORT}/${responseId}/response`, config)
    .then(res => {
      if (res.data) {
        fileDownload(res.data, `${content.title}.docx`);
      }
      return null;
    }).catch((err) => {
      console.log(err);
      return null;
    });
}

export async function createAcademicReport(data, loading = false) {
  const config = { loading };
  return axios
    .post(`${API.DOWNLOAD_ACADEMIC_REPORT}`, (data), config)
    .then(res => {
      if (res?.data) return res.data;
      return null;
    }).catch((err) => {
      console.log(err);
      return null;
    });
}

export async function createSpeakingReport(data) {
  console.log("data", data);
  return axios
    .post(`${API.DOWNLOAD_SPEAKING_REPORT}`, data)
    .then(res => {
      console.log("res", res);
      if (res?.data) return res.data;
      return null;
    }).catch((err) => {
      console.log(err);
      return null;
    });
}
