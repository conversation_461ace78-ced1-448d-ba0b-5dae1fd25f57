import { API } from "@api";
import { createBase, deleteBase, getAllBase, updateBase } from "../../Base";

export const getAllConversation = (query) => {
  return getAllBase(API.CONVERSATION, query);
};
export const createConversation = (data) => {
  return createBase(API.CONVERSATION, data);
};

export const copyConversation = (data, toastError = false) => {
  return createBase(`${API.CONVERSATION}/copy`, data, [], false, toastError);
};
export const editConversation = (id, data, toastError = false) => {
  return updateBase(API.CONVERSATION_DETAILS.format(id), data, [], false, toastError);
};

export const approveConversation = (id, data, toastError = false) => {
  return updateBase(API.CONVERSATION_DETAIL_APPROVE.format(id), data, [], false, toastError);
};
export const deleteConversation = (id, toastError = false) => {
  return deleteBase(API.CONVERSATION_DETAILS, id, false, toastError);
};
