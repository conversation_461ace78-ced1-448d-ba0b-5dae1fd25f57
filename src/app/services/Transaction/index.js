import { API } from "@api";
import {
  getDetailBase,
  getAllBase, getBase,
} from "@services/Base";
import axios from "axios";

export async function getAllTransactionByUser(loading) {
  const config = { loading };
  return axios.get(`${API.PAYMENT_HISTORY}`, config)
    .then(response => {
      if (response.status === 200) return response?.data;
    })
    .catch((err) => {
      return null;
    })
}

export async function getAllTransactionByUserID(id) {
  return getDetailBase(API.PAYMENT_HISTORY_USER_ID, id)
}

export function getTransactionDetail(id) {
  return getDetailBase(API.TRANSACTION_ID, id,);
}