import StudentBreadcrumb from '@app/layout/StudentLayout/StudentBreadcrumb';
import {getShadowingExerciseDetails, resetAnswerDictation} from '@app/services/DictationShadowing';
import {convertSnakeCaseToCamelCase} from '@common/dataConverter';
import {toast} from '@component/ToastProvider';
import {RECOGNIZE_STATUS} from '@constant';
import AudioPlayer from '@src/app/component/AudioPlayer/AudioPlayer';
import ArrowLeftIcon from '@src/assets/icons/arrow-left-square.svg';
import ArrowRightIcon from '@src/assets/icons/arrow-right-square.svg';
import HideIcon from '@src/assets/icons/hide.svg';
import ResetIcon from '@src/assets/icons/reset.svg';
import ShowIcon from '@src/assets/icons/show.svg';
import TickSquareIcon from '@src/assets/icons/tick-square.svg';
import UnTickSquareIcon from '@src/assets/icons/untick-square.svg';
import {Button, Select, Spin, message, Popover} from 'antd';
import React, {useCallback, useEffect, useState, useRef} from 'react';
import {useTranslation} from 'react-i18next';
import {useParams} from 'react-router-dom';
import './ShadowingScreen.scss';
import SpeakingRecordedAudioPlayer from './SpeakingRecordedAudioPlayer';
import SpeakingRecorder from './SpeakingRecorder';
import {API} from '@api';
import TranscriptDisplay from '../DictationScreen/TranscriptDisplay';
import Loading from '@src/app/component/Loading';
const {Option} = Select;

export const ShadowingContext = React.createContext();
export const useShadowing = () => React.useContext(ShadowingContext);

export const ShadowingScreen = () => {
  const {t} = useTranslation();
  const {id: topicId} = useParams();
  const recorderRef = useRef(null);
  const transcriptContainerRef = useRef(null);
  const activeItemRef = useRef(null);

  // Data states
  const [topicData, setTopicData] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [progress, setProgress] = useState({current: 0, total: 0, correctCount: 0});
  const [settings, setSettings] = useState({
    showTranscript: true,
    showHints: true,
    autoPlay: true,
    playbackSpeed: 1,
  });
  const [showExerciseText, setShowExerciseText] = useState(true);

  // UI states
  const [loading, setLoading] = useState(true);
  const [activeTranscriptIndex, setActiveTranscriptIndex] = useState(null);
  const [recognizeStatus, setRecognizeStatus] = useState(RECOGNIZE_STATUS.NOT_STARTED);
  const [recordResult, setRecordResult] = useState([]);
  const [text, setText] = useState('');
  const [projectData, setProjectData] = useState(null);
  const [wordAnalyzed, setWordAnalyzed] = useState([]);
  const [countErrors, setCountErrors] = useState({});
  const [audioFileId, setAudioFileId] = useState(null);
  const [audioUrlDownload, setAudioUrlDownload] = useState(null);
  const [isContentTooShort, setContentTooShort] = useState(false);
  const [showErrors, setShowErrors] = useState({
    mispronunciation: true,
    unexpectedBreak: true,
    missingBreak: true,
    monotone: true,
  });
  const [exerciseData, setExerciseData] = useState(null);

  // Get current question
  const currentQuestion = questions[currentQuestionIndex];

  const handlePlaybackSpeedChange = newSpeed => {
    setSettings(prev => ({
      ...prev,
      playbackSpeed: newSpeed,
    }));
  };

  const handleAudioProgress = useCallback(
    playedSeconds => {
      const activeIndex = questions.findIndex(
        question => playedSeconds >= question.audioStart && playedSeconds <= question.audioEnd,
      );
      setActiveTranscriptIndex(activeIndex);
    },
    [questions],
  );

  // Function to generate hidden text
  const getHiddenText = text => {
    return '*'.repeat(text.length);
  };

  const toggleExerciseText = () => {
    setShowExerciseText(!showExerciseText);
  };

  useEffect(() => {
    function findErrors(word) {
      const breakErrors = word?.pronunciationAssessment?.feedback?.prosody?.break?.errorTypes;
      const intonationErrors = word?.pronunciationAssessment?.feedback?.prosody?.intonation?.errorTypes;
      const isUnexpectedBreak = breakErrors?.includes('UnexpectedBreak');
      const isMissingBreak = breakErrors?.includes('MissingBreak');
      const isMonotone = intonationErrors?.includes('Monotone');
      const isMispronunciation = word.pronunciationAssessment?.accuracyScore < 60;

      return {
        isUnexpectedBreak,
        isMissingBreak,
        isMonotone,
        isMispronunciation,
      };
    }

    const errorTemp = {
      unexpectedBreak: 0,
      missingBreak: 0,
      monotone: 0,
      mispronunciation: 0,
    };

    const words = recordResult
      ?.flatMap(item => item.nBest.flatMap(nBestItem => nBestItem.words))
      .filter(item => !!item?.word)
      .map(word => {
        const {isUnexpectedBreak, isMissingBreak, isMonotone, isMispronunciation} = findErrors(word);
        if (isUnexpectedBreak) errorTemp.unexpectedBreak += 1;
        if (isMissingBreak) errorTemp.missingBreak += 1;
        if (isMonotone) errorTemp.monotone += 1;
        if (isMispronunciation) errorTemp.mispronunciation += 1;

        word.errors = {
          unexpectedBreak: isUnexpectedBreak,
          missingBreak: isMissingBreak,
          monotone: isMonotone,
          mispronunciation: isMispronunciation,
        };

        return word;
      });

    setWordAnalyzed(words);
    setCountErrors(errorTemp);
    setContentTooShort(recordResult?.length && words?.length === 0);
  }, [recordResult]);

  const formatQuestion = exercise => ({
    id: exercise._id || exercise?.segment?._id,
    type: 'sentence', // Shadowing exercises are sentence-based
    text: exercise.segment.text,
    answer: exercise.segment.text,
    audioStart: exercise.segment.start,
    audioEnd: exercise.segment.end,
    transcript: exercise.segment.text,
    // Keep segment data for reference
    segment: exercise.segment,
    submissionId: exercise._id,
    // Add student response data if available
    studentAnswer: exercise.studentAnswer || '',
    accuracyScore: exercise.accuracyScore || 0,
    standardResults: exercise.standardResults || [],
    errorLists: exercise.errorLists || [],
    studentAudioId: exercise.studentAudioId || null,
    exerciseType: exercise.exerciseType || 'shadowing',
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await getShadowingExerciseDetails(topicId);

        if (!response) {
          throw new Error('Invalid response data');
        }

        const exerciseData = response;
        setExerciseData(exerciseData);

        // Format questions from the API response
        const formattedQuestions = exerciseData.exercises.map(formatQuestion);

        // Set topic data
        const topicInfo = {
          id: exerciseData._id,
          title: exerciseData.name,
          level: exerciseData.difficulty,
          description: exerciseData.tag,
          audio: {
            url: API.STREAM_MEDIA.format(exerciseData.audioId),
            duration: formattedQuestions.reduce((total, q) => Math.max(total, q.audioEnd), 0),
          },
        };

        setTopicData(topicInfo);
        setQuestions(formattedQuestions);
        setProgress({
          current: 0,
          total: formattedQuestions.length,
          correctCount: 0,
          score: 0,
        });

        // Set default settings if not provided by API
        setSettings({
          showTranscript: true,
          showHints: true,
          autoPlay: true,
          playbackSpeed: 1,
        });

        // Reset states
        setCurrentQuestionIndex(0);
      } catch (error) {
        console.error('Error loading exercise:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [topicId, t]);

  const handleNext = useCallback(() => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
      setProgress(prev => ({
        ...prev,
        current: prev.current + 1,
      }));
    }
  }, [currentQuestionIndex, questions.length]);

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
      setProgress(prev => ({
        ...prev,
        current: prev.current - 1,
      }));
    }
  };

  const handleTranscriptToggle = checked => {
    setSettings(prev => ({
      ...prev,
      showTranscript: checked,
    }));
  };

  const handleKeyPress = useCallback(
    event => {
      if (event.key === 'Enter' && recognizeStatus !== RECOGNIZE_STATUS.RECOGNIZING) {
        handleNext();
      } else if (event.key === ' ') {
        event.preventDefault();
        const startStates = [RECOGNIZE_STATUS.NOT_STARTED, RECOGNIZE_STATUS.COMPLETE];
        if (startStates.includes(recognizeStatus)) {
          console.log('Attempting to start recording via Spacebar...');
          recorderRef.current?.startRecording();
        } else if (recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING) {
          console.log('Attempting to stop recording via Spacebar...');
          recorderRef.current?.stopRecording();
        }
      }
    },
    [recognizeStatus, handleNext, recorderRef],
  );

  useEffect(() => {
    window.addEventListener('keydown', handleKeyPress);
    return () => {
      window.removeEventListener('keydown', handleKeyPress);
    };
  }, [handleKeyPress]);

  useEffect(() => {
    setAudioFileId(currentQuestion?.studentAudioId || null);
    setAudioUrlDownload(currentQuestion?.studentAudioId ? API.STREAM_ID.format(currentQuestion?.studentAudioId) : null);
  }, [currentQuestion]);

  // Update correctCount whenever questions change
  useEffect(() => {
    if (questions.length > 0) {
      const questionsFiltered = questions.filter(q => q.studentAnswer);
      const totalAccuracy = questionsFiltered.reduce((sum, q) => sum + (q.accuracyScore || 0), 0);
      const averageAccuracy = questionsFiltered.length > 0 ? Math.round(totalAccuracy / questionsFiltered.length) : 0;
      setProgress(prev => ({
        ...prev,
        correctCount: averageAccuracy,
      }));
    }
  }, [questions]);

  // Add effect to scroll active transcript into view
  useEffect(() => {
    if (activeTranscriptIndex !== null && transcriptContainerRef.current && activeItemRef.current) {
      const container = transcriptContainerRef.current;
      const activeItem = activeItemRef.current;

      const containerRect = container.getBoundingClientRect();
      const activeItemRect = activeItem.getBoundingClientRect();

      // Check if the active item is outside the visible area
      if (activeItemRect.top < containerRect.top || activeItemRect.bottom > containerRect.bottom) {
        // Scroll the item into view with smooth behavior
        activeItem.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
        });
      }
    }
  }, [activeTranscriptIndex]);

  function handleDataResponse(socket) {
    socket.on('error', err => {
      socket.disconnect();
      setRecognizeStatus(RECOGNIZE_STATUS.COMPLETE);
    });

    socket.on('finish-recognition', message => {
      socket.disconnect();
      setRecognizeStatus(RECOGNIZE_STATUS.COMPLETE);
      console.log('Submission ID:', message.submission);

      if (message.submission) {
        // Update questions array with the new submission data
        setQuestions(prevQuestions => {
          const updatedQuestions = [...prevQuestions];
          // Find the current question index
          const index = currentQuestionIndex;

          // Use formatQuestion to ensure consistent formatting
          const updatedQuestion = formatQuestion(message.submission);
          updatedQuestions[index] = updatedQuestion;

          return updatedQuestions;
        });
        setAudioFileId(message.submission.studentAudioId || '');
        setAudioUrlDownload(
          message.submission.studentAudioId ? API.STREAM_ID.format(message.submission.studentAudioId) : null,
        );
      }
    });
    socket.on('message', message => {
      let results;
      switch (message.state) {
        case 'recognizing':
          setText(message.recognizing);
          break;
        case 'sentence_score':
          setText('');
          results = convertSnakeCaseToCamelCase([message.results]);
          setRecordResult(prevState => [...prevState, ...results]);
          break;
        case 'overall_score':
          setRecordResult(convertSnakeCaseToCamelCase(message.results));
          break;
        case 'recognized_text':
          console.log('recognized_text', message);
          break;
        case 'content_score':
          console.log('content_score', message);
          break;
        case 'audio_file_saved':
          setAudioFileId(message.fileId || '');
          setAudioUrlDownload(message.fileId ? API.STREAM_ID.format(message.fileId) : null);
          break;
        case 'error':
          console.log('error', message);
          socket.disconnect();
          setRecognizeStatus(RECOGNIZE_STATUS.COMPLETE);
          toast.error('AN_ERROR_OCCURRED_PLEASE_CONTACT_THE_ADMINISTRATOR');
          break;
        default:
          break;
      }
    });
  }

  const handleResetExercise = async () => {
    if (!currentQuestion) return;
    setRecordResult([]);
    setWordAnalyzed([]);
    setCountErrors({});
    setAudioFileId(null);
    setAudioUrlDownload(null);
    setShowErrors({
      mispronunciation: true,
      unexpectedBreak: true,
      missingBreak: true,
      monotone: true,
    });
    setText('');
    if (currentQuestion.submissionId) {
      try {
        // Create the request body according to the required format
        const requestBody = {
          submissionId: currentQuestion.submissionId,
        };

        const response = await resetAnswerDictation(requestBody);
        if (response.error) {
          return;
        }

        // Update the current question with response data
        setQuestions(prevQuestions => {
          const newQuestions = [...prevQuestions];
          const formattedQuestion = {
            ...formatQuestion(response),
          };
          newQuestions[currentQuestionIndex] = formattedQuestion;
          return newQuestions;
        });
      } catch (error) {
        console.error('Error checking answer:', error);
      }
    }
  };

  if (loading) {
    return (
      <div className="dictation-screen">
        <div className="dictation-screen__loading">
          <Loading active transparent />
        </div>
      </div>
    );
  }

  return (
    <div className="shadowing-screen">
      <StudentBreadcrumb subTitle={topicData?.title} />
      <div className="shadowing-screen__container">
        {topicData && (
          <div className="shadowing-screen__topic-info">
            <h1>{topicData.title}</h1>
          </div>
        )}

        {currentQuestion && (
          <>
            <div className="shadowing-screen__audio-player">
              <AudioPlayer
                audioUrl={topicData?.audio?.url}
                startTime={currentQuestion.audioStart}
                endTime={currentQuestion.audioEnd}
                playbackSpeed={settings.playbackSpeed}
                autoPlay={settings.autoPlay}
                onPlaybackSpeedChange={handlePlaybackSpeedChange}
              />
            </div>
            <div className="shadowing-screen__header">
              <Button className="reset-button" type="text" onClick={handleResetExercise}>
                <img src={ResetIcon} alt="Reset exercise" />
                <span>{t('RESET')}</span>
              </Button>
              <div className="shadowing-screen__progress">
                {progress.current + 1}/{progress.total}
              </div>
              <div className="navigation-group">
                <Button
                  className={`nav-button ${currentQuestionIndex === 0 ? 'disabled' : ''}`}
                  type="text"
                  disabled={currentQuestionIndex === 0 || recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING}
                  onClick={handlePrevious}
                >
                  <img src={ArrowLeftIcon} alt="Previous question" />
                </Button>
                <Button
                  className={`nav-button ${currentQuestionIndex >= questions.length - 1 ? 'disabled' : ''}`}
                  type="text"
                  disabled={
                    currentQuestionIndex >= questions.length - 1 || recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING
                  }
                  onClick={handleNext}
                >
                  <img src={ArrowRightIcon} alt="Next question" />
                </Button>
              </div>
            </div>
            <div className="shadowing-screen__exercise">
              <div className="exercise-text">
                <div className="exercise-text__content">
                  <span>{showExerciseText ? currentQuestion.text : getHiddenText(currentQuestion.text)}</span>
                  <img
                    src={showExerciseText ? ShowIcon : HideIcon}
                    alt={showExerciseText ? t('HIDE_TEXT') : t('SHOW_TEXT')}
                    onClick={toggleExerciseText}
                    className="toggle-text-icon"
                  />
                </div>
              </div>
            </div>
            <ShadowingContext.Provider
              value={{
                recognizeStatus,
                setRecognizeStatus,
                recordResult,
                setRecordResult,
                text,
                setText,
                projectData,
                setProjectData,
                wordAnalyzed,
                setWordAnalyzed,
                handleDataResponse,
                countErrors,
                setCountErrors,
                audioFileId,
                setAudioFileId,
                audioUrlDownload,
                setAudioUrlDownload,
                showErrors,
                setShowErrors,
                currentQuestion,
                exerciseData,
              }}
            >
              <div className="shadowing-screen__speaking-recorder">
                <SpeakingRecorder ref={recorderRef} />
              </div>
              <div className="shadowing-screen__speaking-result">
                {currentQuestion.standardResults?.length > 0 ? (
                  <div className="result-content">
                    <div className="result-text">
                      {currentQuestion.standardResults.map((item, index) => (
                        <Popover
                          key={index}
                          overlayClassName="speaking-word__popover"
                          title={`${item.word}: ${Math.round(item.accuracy)}%`}
                          content={
                            <div className="speaking-word__accuracy-evaluation">
                              <div className="accuracy-evaluation__item">
                                <span>{item.accuracy >= 80 ? t('EXCELLENT') : t('TRY_AGAIN')}</span>
                              </div>
                            </div>
                          }
                          trigger="hover"
                          mouseEnterDelay={0.05}
                          mouseLeaveDelay={0.05}
                          destroyTooltipOnHide={true}
                          placement="topLeft"
                        >
                          <span style={{color: item.accuracy < 80 ? 'red' : 'black'}}>{item.word}</span>
                        </Popover>
                      ))}
                    </div>
                    <div className="result-score">{Math.round(currentQuestion.accuracyScore)}%</div>
                  </div>
                ) : (
                  <div className="speaking-result__placeholder">{t('SHADOWING_PLACEHOLDER')}</div>
                )}
              </div>
              <div className="shadowing-screen__actions">
                <Button
                  className="next-button"
                  onClick={handleNext}
                  disabled={
                    currentQuestionIndex >= questions.length - 1 || recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING
                  }
                >
                  {t('NEXT')}
                </Button>
              </div>

              <div style={{display: questions.some(q => q.studentAnswer) ? 'block' : 'none'}}>
                <div className="shadowing-screen__score-card">
                  <div className="score-card__content">
                    <div className="score-card__count">
                      <span className="correct-count">{progress.correctCount}%</span>
                    </div>
                    <div className="score-card__title">{t('ACCURACY_SCORE')}</div>
                  </div>
                </div>

                <div className="shadowing-screen__speaking-audio-recorded">
                  <SpeakingRecordedAudioPlayer />
                </div>
              </div>
            </ShadowingContext.Provider>

            {progress.current === questions.length - 1 && (
              <div className="shadowing-screen__transcript">
                <div className="transcript-toggle">
                  <div className="custom-checkbox" onClick={() => handleTranscriptToggle(!settings.showTranscript)}>
                    <img
                      src={settings.showTranscript ? TickSquareIcon : UnTickSquareIcon}
                      alt={settings.showTranscript ? t('HIDE_TRANSCRIPT') : t('SHOW_TRANSCRIPT')}
                    />
                    <span>{t('SHOW_TRANSCRIPT')}</span>
                  </div>
                </div>
                {settings.showTranscript && (
                  <div className={`transcript-content`}>
                    <div className="transcript-content__audio-player">
                      <AudioPlayer
                        audioUrl={topicData?.audio?.url}
                        startTime={0}
                        endTime={topicData?.audio?.duration}
                        playbackSpeed={settings.playbackSpeed}
                        autoPlay={false}
                        onPlaybackSpeedChange={handlePlaybackSpeedChange}
                        onProgress={handleAudioProgress}
                      />
                    </div>
                    <TranscriptDisplay
                      questions={questions}
                      activeTranscriptIndex={activeTranscriptIndex}
                      transcriptContainerRef={transcriptContainerRef}
                      activeItemRef={activeItemRef}
                    />
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};
