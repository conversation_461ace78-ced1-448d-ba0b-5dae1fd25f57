import { BUTTON } from "@constant";
import AntButton from "@src/app/component/AntButton";
import ChevronDown from "@src/app/component/SvgIcons/ChevronDown";
import { Checkbox, Collapse } from "antd";
import { useTranslation } from "react-i18next";

import { TYPE_OF_TOOL, STUDENT_TOOL_BASE_TYPE } from "@constant";

const FillterEssay = ({ writingTools, tagCategoryData, ...props }) => {
  const { categories, setCategories } = props;
  const { t } = useTranslation();

  const onChangeCategorys = (checkedValues, key) => {
    setCategories(pre => {
      const newState = { ...pre };
      if (!checkedValues?.length) {
        delete newState[key];
        return newState;
      }
      return ({
        ...pre,
        [key]: checkedValues
      })
    });
  }

  const onChangeInputType = (inputType, checked) => {
    if (!checked) {
      let newCategories = { ...categories };
      delete newCategories[inputType];
      setCategories(newCategories);
    } else {
      setCategories(pre => ({ ...pre, [inputType]: tagCategoryData?.[inputType]?.categories }));
    }
  }

  const renderKey = (inputType) => {
    if ([TYPE_OF_TOOL.MARK_TEST_TASK_1, TYPE_OF_TOOL.STUDENT_TASK_1].includes(inputType)) return STUDENT_TOOL_BASE_TYPE.writing_1;
    return STUDENT_TOOL_BASE_TYPE.writing_2;
  }

  const generateToolCheckbox = (key, label) => {
    const indeterminate = categories[key]?.length > 0
      && categories[key]?.length < tagCategoryData?.[key]?.categories?.length;
    return <Checkbox
      key={key}
      checked={categories[key] && categories[key]?.length === tagCategoryData?.[key]?.categories?.length}
      onChange={e => onChangeInputType(key, e.target.checked)}
      indeterminate={indeterminate}
      className="filter-assign__section-collapse__checkbox-tool"
    >
      {label}
    </Checkbox>
  };

  const collapseItems = writingTools.toReversed().map((item, index) => {
    const key = renderKey(item?.inputType);
    return ({
      key: index,
      label: generateToolCheckbox(key, item.name),
      children: <Checkbox.Group
        className="filter-assign__section-collapse__checkbox-category"
        onChange={(checkedValues) => onChangeCategorys(checkedValues, key)}
        value={categories[key]}
        options={tagCategoryData?.[key]?.categories}
      />
    });
  });

  const renderExpandIcon = ({ children }) => {
    if (!children?.props?.options?.length) return null;
    return <AntButton type={BUTTON.GHOST_WHITE} size="xsmall" icon={<ChevronDown />} />
  }

  return <div className="filter-assign__section">
    <div className="filter-assign__section-header">
      {t("ESSAYS")}
    </div>
    <div className="filter-assign__section-body">
      <Collapse
        collapsible="icon"
        expandIconPosition="end"
        expandIcon={renderExpandIcon}
        ghost
        className="filter-assign__section-collapse"
        items={collapseItems}
      />
    </div>
  </div>
}

export default FillterEssay;