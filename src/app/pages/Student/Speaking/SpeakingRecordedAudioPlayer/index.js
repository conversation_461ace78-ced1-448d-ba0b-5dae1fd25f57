import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import Slider from "rc-slider";
import { Button } from "antd";
import ReactPlayer from "react-player";
import { Link } from "react-router-dom";

import { toast } from "@component/ToastProvider";
import { useSpeaking } from "@app/pages/Student/Speaking";
import AntButton from "@component/AntButton";
import Loading from "@component/Loading";

import { API } from "@api";
import { BUTTON } from "@constant";
import { renderAudioDuration } from "@common/functionCommons";

import ArrowDown from "@component/SvgIcons/ArrowDown";

import SPEAKING_PAUSE from "@src/asset/icon/pause/speaking-pause.svg";
import SPEAKING_PLAY from "@src/asset/icon/play/speaking-play.svg";

import "./SpeakingRecordedAudioPlayer.scss";
import { actions, paramsCreators, TRACKING_ACTIONS } from "@src/ducks/tracking.duck";
import { useDispatch } from "react-redux";


function SpeakingRecordedAudioPlayer() {
  const { t } = useTranslation();
  const { audioFileId, audioUrlDownload } = useSpeaking();
  const { setAudioTooShort } = useSpeaking();
  
  const [, updateState] = useState();
  const forceUpdate = useCallback(() => updateState({}), []);
  
  const [isLoaded, setLoaded] = useState(false);
  
  const playerRef = useRef(null);
  
  const [isPlaying, setPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [audioDuration, setAudioDuration] = useState(0);
  const dispatch = useDispatch();
  
  
  useEffect(() => {
    setAudioTooShort(!!audioFileId && audioDuration < 10);
  }, [audioFileId, audioDuration]);
  
  
  useEffect(() => {
    setLoaded(false);
    
    if (!audioFileId) {
      setCurrentTime(0);
      setAudioDuration(0);
      setPlaying(false);
    } else {
    }
    
  }, [audioFileId]);
  
  function handleError(a, b, c, d) {
    setLoaded(true);
    return toast.error(t("RECORDING_DOES_NOT_EXIST"));
  }
  
  return <div id="js-speaking-audio-player" className="speaking-section">
    <div className="speaking-section__title">
      <span className="speaking-section__title-text">
        {t("RECORDING")}
      </span>
      <span className="speaking-section__title-action">
        {!!audioUrlDownload && !!audioDuration
          ? <Link to={audioUrlDownload} target="_blank" download>
            <AntButton size="xsmall" icon={<ArrowDown/>} type={BUTTON.WHITE_COBALT} onClick={
              () => dispatch(actions.trackCustomClick(TRACKING_ACTIONS.CLICK_DOWNLOAD))}/>
          </Link>
          : <AntButton disabled size="xsmall" icon={<ArrowDown/>} type={BUTTON.WHITE_COBALT}/>}
      </span>
    </div>
    
    
    <Loading active={!isLoaded && !!audioFileId} className="speaking-recorded-audio-player">
      <div className="audio-player__play">
        <Button
          shape="circle"
          className="ant-btn-compact"
          icon={isPlaying ? <img src={SPEAKING_PAUSE} alt=""/> : <img src={SPEAKING_PLAY} alt=""/>}
          onClick={() => {
            if (!audioDuration) return;
            setPlaying(prevState => !prevState);
          }}
          //disabled={!audioDuration}
        />
      </div>
      
      <div className="audio-player__time">
        {renderAudioDuration(currentTime)}
      </div>
      <div className="audio-player__seek-bar">
        <Slider
          min={0}
          max={audioDuration}
          value={currentTime}
          onChange={value => {
            if (!audioDuration) return;
            setCurrentTime(value);
            playerRef.current.seekTo(value);
          }}
        />
      </div>
      <div className="audio-player__time">
        {renderAudioDuration(audioDuration)}
      </div>
    </Loading>
    
    
    {audioFileId && <ReactPlayer
      ref={playerRef}
      url={API.STREAM_MEDIA.format(audioFileId)}
      playing={isPlaying}
      width="0"
      height="0"
      progressInterval={0}
      onDuration={duration => {
        setAudioDuration(Math.round(duration));
        setLoaded(true);
      }}
      onError={handleError}
      onProgress={({ playedSeconds }) => setCurrentTime(Math.floor(playedSeconds))}
      onEnded={() => {
        setCurrentTime(0);
        setPlaying(false);
        playerRef.current.seekTo(0);
      }}
    />}
  </div>;
}

export default SpeakingRecordedAudioPlayer;