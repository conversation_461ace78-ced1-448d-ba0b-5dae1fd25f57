import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { connect, useDispatch } from "react-redux";

import { toast } from "@component/ToastProvider";
import { useSpeaking } from "@app/pages/Student/Speaking";
import AntButton from "@component/AntButton";
import OutputLanguage from "@component/OutputLanguage";

import { BUTTON, CONSTANT, LANGUAGE, RECOGNIZE_STATUS } from "@constant";

import { getProjectDetail } from "@services/Project";
import { submitSpeaking } from "@services/Content";

import TwinStars25 from "@component/SvgIcons/TwinStars/TwinStars25";
import INFO_CIRCLE_ERROR from "@src/asset/icon/info/info-circle-error.svg";
import SUBMITTING_SPEAKING from "@src/asset/icon/submitting-speaking.svg";
import ROLLING from "@src/asset/gif/rolling.gif";

import "./SpeakingGetFeedback.scss";
import { actions, TRACKING_ACTIONS } from "@src/ducks/tracking.duck";

function SpeakingGetFeedback({ studentTools }) {
  const { t } = useTranslation();

  const { projectData, isContentTooShort, recognizeStatus } = useSpeaking();
  const { responseData, setResponseData, isShowFeedback, disabledEdit } = useSpeaking();

  const [langSelected, setLangSelected] = useState(LANGUAGE.VI);
  const dispatch = useDispatch();

  const [isProcessing, isError] = useMemo(() => {
    return [
      responseData?.state?.toUpperCase() === CONSTANT.PROCESSING,
      responseData?.state?.toUpperCase() === CONSTANT.ERROR || responseData?.output?.state === "invalid",
    ];
  }, [responseData?.state]);

  async function handleGetFeedback() {
    if (isProcessing) return;
    
    const projectInfo = await getProjectDetail(projectData?._id);
    const workspaceId = projectInfo?.data?.workspaceId;
    const contentId = projectInfo?.data?.content?.[0]?._id;
    if (!contentId) return toast.error("AN_ERROR_OCCURRED");
    
    const apiRequest = {
      workspaceId,
      contentId,
      inputType: studentTools.speaking.inputType,
      inputData: {
        language: langSelected === LANGUAGE.VI ? "vietnamese" : "english",
        instructionId: studentTools.speaking?.instructionIds?.[0]?._id,
      },
    };
    setResponseData({ state: CONSTANT.PROCESSING });
    const apiResponse = await submitSpeaking(apiRequest);
    if (apiResponse) {
      setResponseData(apiResponse);
    } else {
      setResponseData({ state: CONSTANT.ERROR });
    }
    dispatch(actions.trackCustomClick(TRACKING_ACTIONS.SUBMIT));
  }

  if (isContentTooShort || recognizeStatus !== RECOGNIZE_STATUS.COMPLETE || isShowFeedback) return null;

  return (
    <div className="speaking-get-feedback">
      <OutputLanguage
        disabled={isProcessing || disabledEdit}
        value={langSelected}
        onChange={(language) => {
          setLangSelected(language);
          dispatch(actions.trackSelectLanguageResult(language));
        }}
      />

      <div className="speaking-submit">
        <AntButton
          size="large"
          type={BUTTON.DEEP_NAVY}
          icon={
            isError ? (
              <img src={INFO_CIRCLE_ERROR} alt="" />
            ) : isProcessing ? (
              <img className="rolling-icon" src={ROLLING} alt="" />
            ) : (
              <TwinStars25 />
            )
          }
          iconLocation={isProcessing || isError ? CONSTANT.LEFT : CONSTANT.RIGHT}
          onClick={handleGetFeedback}
          disabled={disabledEdit}
        >
          {t(isProcessing ? "SUBMITTING" : isError ? "SUBMIT_FAILED" : "SUBMIT")}
        </AntButton>
      </div>

      {isProcessing && (
        <div className="speaking-submitting">
          <img className="speaking-submitting__image" src={SUBMITTING_SPEAKING} alt="" />
          <div className="speaking-submitting__text">{`${t("YOUR_FEEDBACK_IS_ON_THE_WAY")},`}</div>
          <div className="speaking-submitting__text">{`${t("PLEASE_WAIT_A_MOMENT")}...`}</div>
        </div>
      )}
    </div>
  );
}

function mapStateToProps(store) {
  const { studentTools } = store.tool;
  return { studentTools };
}

export default connect(mapStateToProps)(SpeakingGetFeedback);
