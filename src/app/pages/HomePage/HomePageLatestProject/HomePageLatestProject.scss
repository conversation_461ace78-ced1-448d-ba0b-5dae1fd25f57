.latest-project-list {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 24px;

  @media screen and (max-width: 1535.98px) {
    grid-template-columns: repeat(6, 1fr);

    div:nth-child(n+7) {
      display: none;
    }
  }

  @media screen and (max-width: 1366px) {
    grid-template-columns: repeat(4, 1fr);

    div:nth-child(n+5) {
      display: none;
    }
  }

  @media screen and (max-width: 800px) {
    grid-template-columns: repeat(2, 1fr);

    div:nth-child(n+3) {
      display: none;
    }
  }
}