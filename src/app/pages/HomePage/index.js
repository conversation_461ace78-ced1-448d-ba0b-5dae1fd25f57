import { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import axios from "axios";

import HomePageMyWorkspace from "@app/pages/HomePage/HomePageMyWorkspace";
import HomePageLatestProject from "@app/pages/HomePage/HomePageLatestProject";
import HomePageStarred from "@app/pages/HomePage/HomePageStarred";
import HomePageShareWithMe from "@app/pages/HomePage/HomePageShareWithMe";
import OwleeChat from "@src/app/component/OwleeChat";
import Loading from "@component/Loading";

import { getRecentProjects } from "@services/Project";
import { getAllSharedWithMe } from "@services/Share";
import { getWorkspaceBelongs } from "@services/Workspace";
import { getAllMySaved } from "@services/MySaved";
import { usePageViewTracker } from "@src/ga";

import { WORKSPACE_TYPE } from "@constant";

import * as workspaceRedux from "@src/ducks/workspace.duck";

import "./Homepage.scss";
import { getChatBotInfo } from "@src/app/services/Settings";

function HomePage({ user, availableWorkspaces, ...props }) {
  usePageViewTracker("HomePage");

  const [recentProjectsData, setRecentProjectsData] = useState([]);
  const [sharedWithMeData, setSharedWithMeData] = useState([]);
  const [workspaceData, setWorkspaceData] = useState([]);
  const [starredData, setStarredData] = useState([]);
  const [workspaceActive, setWorkspaceActive] = useState("");
  const [isFirst, setFirst] = useState(true);
  const [chatBotId, setChatBotId] = useState(null);

  useEffect(() => {
    getDataExeptWorkspace();
    getChatBotData();
    if (!availableWorkspaces?.length) {
      props.getAvailableWorkspaces();
    }
  }, []);
  
  useEffect(() => {
    if (availableWorkspaces?.length > 0 && !workspaceActive) {
      const myWorkspaceId = availableWorkspaces.find((workspace) => workspace?.type === WORKSPACE_TYPE.PERSONAL)?._id;
      setWorkspaceActive(myWorkspaceId);
    }
  }, [availableWorkspaces, workspaceActive]);
  
  useEffect(() => {
    if (workspaceActive) {
      getWorkspaceData();
    }
  }, [workspaceActive]);

  const getChatBotData = async () => {
    const response = await getChatBotInfo();
    if (response?.showChatbot && response?.chatbotId) {
      setChatBotId(response?.chatbotId);
    }
  }

  const updateStates = (shareData, recentData, savedData, workspace) => {
    if (shareData) setSharedWithMeData(shareData.map(item => item?.folderId || item?.projectId));
    if (recentData) setRecentProjectsData(recentData);
    if (savedData) setStarredData(savedData.map(item => {
      const dataReturn = item?.folderId || item?.projectId;
      return { ...dataReturn, isSaved: true };
    }));
    if (workspace) {
      setWorkspaceData(workspace);
    }
    setFirst(false)
  }
  
  const getAllData = async () => {
    const allRequest = [
      getAllSharedWithMe({ limit: 8 }),
      getRecentProjects({ userId: user?._id }),
      getAllMySaved({ limit: 8 }),
      getWorkspaceBelongs(workspaceActive, { limit: 9 }),
    ];
    
    const [shareData, recentData, savedData, workspace] = await axios.all(allRequest);
    
    updateStates(shareData, recentData, savedData, workspace);
  };

  const getDataExeptWorkspace = async () => {
    const allRequest = [
      getAllSharedWithMe({ limit: 8 }),
      getRecentProjects({ userId: user?._id }),
      getAllMySaved({ limit: 8 }),
    ];
    
    const [shareData, recentData, savedData] = await axios.all(allRequest);
    updateStates(shareData, recentData, savedData, null);
  };
  
  const getDataExeptShareWithMe = async () => {
    const allRequest = [
      getRecentProjects({ userId: user?._id }),
      getAllMySaved({ limit: 8 }),
      getWorkspaceBelongs(workspaceActive, { limit: 9 }),
    ];
    
    const [recentData, savedData, workspace] = await axios.all(allRequest);
    updateStates(null, recentData, savedData, workspace);
  };

  const getWorkspaceData = async () => {
    const dataResponse = await getWorkspaceBelongs(workspaceActive, { limit: 9 });
    if (dataResponse) {
      setWorkspaceData(dataResponse);
    }
  };
  
  const updateItemArray = (array = [], data = {}) => {
    return array.map((item) => {
      if (item?._id === data?._id) {
        return { ...item, ...data };
      }
      if (data?.folderName && item?.folderId) {
        const folderId = item?.folderId?._id || item?.folderId;
        const inFolder = folderId === data?._id;
        if (inFolder) {
          return { ...item, folderId: { ...item?.folderId, folderName: data?.folderName } };
        }
      }
      return item;
    });
  };
  
  const updateWorkspaceWhenRename = (data = {}) => {
    let newWorkspaceData = [];
    workspaceData.forEach((item) => {
      if (item?._id === data?._id) {
        newWorkspaceData = [{ ...item, ...data }, ...newWorkspaceData];
      } else if (data?.folderName && item?.folderId) {
        const folderId = item?.folderId?._id || item?.folderId;
        const inFolder = folderId === data?._id;
        if (inFolder) {
          newWorkspaceData.push({ ...item, folderId: { ...item?.folderId, folderName: data?.folderName } });
        }
      } else {
        newWorkspaceData.push(item);
      }
    });
    return newWorkspaceData;
  };
  
  const updateChangeStarredItemArray = (array = [], _id) => {
    return array.map((item) => {
      if (item._id === _id) {
        return { ...item, isSaved: !item?.isSaved };
      }
      return item;
    });
  };
  
  const handleAfterRename = (data) => {
    const newWorkspaceData = updateWorkspaceWhenRename(data);
    const newRecentProjects = updateItemArray(recentProjectsData, data);
    const newMySaved = updateItemArray(starredData, data);
    const newSharedWithMeData = updateItemArray(sharedWithMeData, data);
    setWorkspaceData(newWorkspaceData);
    setRecentProjectsData(newRecentProjects);
    setStarredData(newMySaved);
    setSharedWithMeData(newSharedWithMeData);
  };

  const handleAfterChangeStarred = async (data) => {
    const newRecentProjects = updateChangeStarredItemArray(recentProjectsData, data?._id);
    const newWorkspaceData = updateChangeStarredItemArray(workspaceData, data?._id);
    const newSharedWithMeData = updateChangeStarredItemArray(sharedWithMeData, data?._id);
    const newMySaved = await getAllMySaved({ limit: 8 });
    if (newMySaved) {
      setStarredData(newMySaved.map(item => {
        const dataReturn = item?.folderId || item?.projectId;
        return { ...dataReturn, isSaved: true };
      }));
    }
    setRecentProjectsData(newRecentProjects);
    setWorkspaceData(newWorkspaceData);
    setSharedWithMeData(newSharedWithMeData);
  };

  if (isFirst) {
    return <Loading active transparent />;
  }
  
  return <>
    <div id="homepage">
      
      {!!recentProjectsData?.length && <HomePageLatestProject
        dataSource={recentProjectsData}
        handleAfterCopy={getWorkspaceData}
        handleAfterRename={handleAfterRename}
        handleAfterDelete={getAllData}
        handleAfterChangeStarred={handleAfterChangeStarred}
        handleAfterMove={getDataExeptShareWithMe}
      />}
      {!!starredData?.length && <HomePageStarred
        dataSource={starredData}
        handleAfterCopy={getWorkspaceData}
        handleAfterRename={handleAfterRename}
        handleAfterDelete={getAllData}
        handleAfterChangeStarred={handleAfterChangeStarred}
        handleAfterMove={getDataExeptShareWithMe}
      />}
      {!!availableWorkspaces?.length && <HomePageMyWorkspace
        dataSource={workspaceData}
        workspaces={availableWorkspaces}
        workspaceActive={workspaceActive}
        setWorkspaceActive={setWorkspaceActive}
        handleAfterCopy={getWorkspaceData}
        handleAfterRename={handleAfterRename}
        handleAfterDelete={getDataExeptShareWithMe}
        handleAfterChangeStarred={handleAfterChangeStarred}
        handleAfterMove={getDataExeptShareWithMe}
      />}
      {!!sharedWithMeData?.length && <HomePageShareWithMe
        dataSource={sharedWithMeData}
        handleAfterCopy={getWorkspaceData}
        handleAfterRename={handleAfterRename}
        handleAfterDelete={getDataExeptWorkspace}
        handleAfterChangeStarred={handleAfterChangeStarred}
      />}
    </div>
    {chatBotId && <OwleeChat chatBotId={chatBotId} />}
  </>;
}


function mapStateToProps(store) {
  const { user } = store.auth;
  const { availableWorkspaces } = store.workspace;
  return { user, availableWorkspaces };
}

const mapDispatchToProps={
  ...workspaceRedux.actions,
}

export default (connect(mapStateToProps, mapDispatchToProps)(HomePage));
