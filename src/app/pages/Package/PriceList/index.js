import React, { useEffect, useMemo, useState } from "react";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import { Input, Table } from "antd";
import { useTranslation } from "react-i18next";
import clsx from "clsx";

import AntButton from "@component/AntButton";

import CheckIcon from "@src/asset/icon/price/checkIcon.svg";
import UncheckIcon from "@src/asset/icon/price/uncheckIcon.svg";

import { LINK } from "@link";
import { BUTTON, CONSTANT } from "@constant";

import { toast } from "@component/ToastProvider";

import "./PriceList.scss";

PriceList.propTypes = {};

function PriceList({ user, unitPrice, dataPackages, features }) {
  const activePackage = user.subscription?.packageId;
  const [buyMoreData, setBuyMoreData] = useState([1, 1, 1]);
  const { t } = useTranslation();
  const isSystemAdmin = user?.isSystemAdmin;


  const packagesBase = useMemo(() => {
    return dataPackages.filter((item) => item?.type === "base");
  }, [dataPackages]);

  const packagesAddon = useMemo(() => {
    return dataPackages.filter((item) => (!!item?.features && item?.type === CONSTANT.ADDON));
  }, [dataPackages]);

  useEffect(() => {
    const newBuyMoreData = packagesAddon.map((item) => Object.values(item?.features)[0]);
    setBuyMoreData(newBuyMoreData);
  }, [packagesAddon]);

  const featurePackagesBase = useMemo(() => {
    return features?.filter((feature) => feature?.packageType === "base");
  }, [features]);

  const onChangeBuyMoreInput = (index, value) => {
    const newBuyMoreData = [...buyMoreData];
    newBuyMoreData[index] = Math.max(value, 1);
    setBuyMoreData(newBuyMoreData);
  };

  function getUnitAmountByUnitName(data) {
    if (data?.order === 3) return null;
    const VND = new Intl.NumberFormat("vi-VN", {
      currency: "VND",
    });
    let moneyNumber = 0;
    for (let i = 0; i < data?.prices?.length; i++) {
      if (data?.prices[i]?.unitName === unitPrice?.toLowerCase()) {
        moneyNumber = VND.format(data?.prices[i]?.unitAmount);
      }
    }
    return moneyNumber + " VNĐ/ " + t(unitPrice);
  }

  const formatVND = (amount) => {
    if (!amount) return "";

    const currencyFormatter = new Intl.NumberFormat("vi-VN", {
      currency: "VND",
    });

    return `${currencyFormatter.format(amount)} VND`;
  };

  const typeButton = (index) => {
    const buttonTypes = [BUTTON.DEEP_GREEN, BUTTON.DEEP_PURPLE, BUTTON.DEEP_YELLOW];
    return buttonTypes[index] || BUTTON.DEEP_NAVY;
  };

  const getFeatureUnit = (featureIds) => {
    return features.find((feature) => feature?._id === featureIds)?.unit;
  };

  const handleBuyMore = (event, amount) => {
    if (amount < CONSTANT.MINIUM_TRANSACTION) {
      event.preventDefault();
      toast.warning({ description: t("MINIMUM_PAYMENT_AMOUNT").format(formatVND(CONSTANT.MINIUM_TRANSACTION)) });
    }
  }

  const columnsPackage = packagesBase.map((packageItem, index) => ({
    title: () => {
      const priceText = getUnitAmountByUnitName(packageItem);
      const linkTo = priceText ? LINK.PAYMENT_ID.format(packageItem._id) : null;
      const packageState = { unitPrice };

      const isFree = packageItem.order === 1;
      const isCurrent = activePackage?._id === packageItem?._id;
      const isRenewal = isCurrent && !isFree;

      const currentTitle = isRenewal ? t("RENEWAL_NOW") : t("CURRENT_PACKAGE");
      const buttonTitle = isCurrent ? currentTitle : priceText ? t("UPGRADE_NOW") : t("CONTACT_US");
      const buttonDisabled = (isFree && isCurrent) || activePackage?.order > packageItem?.order;

      return (
        <div className="clickee-price-title">
          <span className={`clickee-price-title__title clickee-price-title__titleIndex${index}`}>
            {packagesBase[index]?.name}
          </span>
          <span className="clickee-price-title__price">{priceText || t("ANNUAL_ONLY")}</span>
          <Link to={linkTo} state={packageState}>
            <AntButton size="large" type={typeButton(index)} disabled={buttonDisabled}>
              {buttonTitle}
            </AntButton>
          </Link>
        </div>
      );
    },
    dataIndex: "price",
    key: "price",
    width: 266,
    render: (_, record) => {
      const isBoolean = record?.type === "Boolean";
      const packageFeature = packageItem?.features[record?._id];
      const hasUnit = Boolean(record?.unit);

      return (
        <div className="price-cell">
          {isBoolean && (
            <div>{packageFeature ? <img src={CheckIcon} alt="check" /> : <img src={UncheckIcon} alt="uncheck" />}</div>
          )}
          {!isBoolean && (
            <>
              <span className={clsx({ "price-cell__price": hasUnit })}>{packageFeature}</span>{" "}
              <span>{record?.unit}</span>
            </>
          )}
          {!record?.type && <img src={UncheckIcon} alt="uncheck" />}
        </div>
      );
    },
  }));

  const columns = [
    {
      title: null,
      dataIndex: "name",
      key: "name",
      width: 276,
      render: (_, values, index) => {
        return <span>{values?.name}</span>;
      },
    },
    ...columnsPackage,
  ];


  return (
    <div className="price-list-container">
      <div className="table-price-list">
        {featurePackagesBase && !!featurePackagesBase.length && (
          <Table
            dataSource={featurePackagesBase}
            columns={columns}
            pagination={false}
            bordered={false}
          // scroll={{ x: 1300 }}
          />
        )}
      </div>
      <div className={"table-buy-more-addon"}>
        {packagesAddon?.map((packageItem, index) => {
          const featureUnit = getFeatureUnit(Object.keys(packageItem?.features)[0]);
          const featureValue = Object.values(packageItem?.features)[0];
          const unitAmount = packageItem?.prices[0]?.unitAmount;
          const unitName = packageItem?.prices[0]?.unitName;
          const packagePrice = `${formatVND(unitAmount)} ${t("FOR")} ${featureValue} ${featureUnit}`;
          const packageInfo = unitName ? `${packagePrice} / ${t(unitName?.toUpperCase())}` : packagePrice;
          const totalAmount = buyMoreData[index] * unitAmount / featureValue;

          const linkTo = totalAmount < CONSTANT.MINIUM_TRANSACTION ? null : LINK.PAYMENT_ID.format(packageItem._id);
          const state = { quantity: buyMoreData[index] };
          return (
            <div className={"table-item-buy-more-addon"} key={index}>
              <div className={`buy-more-items `}>
                <span className={`buy-more-items__title item-addon-${index}`}>
                  {t("BUY_MORE")} {packageItem?.name}
                </span>
                <span className={"buy-more-items__info"}>
                  {packageInfo}
                </span>
                <div className={"buy-more-items__priceList"}>
                  <span className={"buy-more-items__priceList__textLeft"}>{t("BUY_MORE")}</span>
                  <Input
                    type={"number"}
                    changeOnWheel={false}
                    min={0}
                    value={buyMoreData[index]}
                    // defaultValue={featureValue}
                    step={featureValue}
                    rootClassName={"buy-more-items__priceList__inputNumber"}
                    size={"large"}
                    onChange={(e) => onChangeBuyMoreInput(index, e?.target?.value)}
                  />
                  <span>{featureUnit}</span>
                </div>
                <div className={"buy-more-items__totalAmount"}>
                  <span>{t("TOTAL_AMOUNT")}</span>
                  <span>
                    {formatVND(totalAmount)}
                  </span>
                </div>
                <div className={"buy-more-items__btnBuy"}>
                  <Link to={linkTo} state={state} onClick={(e) => handleBuyMore(e, totalAmount)}>
                    <AntButton
                      type={BUTTON.DEEP_NAVY}
                      size={"large"}
                      // disabled={!isSystemAdmin}
                    >
                      {t("BUY_NOW")}
                    </AntButton>
                  </Link>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(PriceList);
