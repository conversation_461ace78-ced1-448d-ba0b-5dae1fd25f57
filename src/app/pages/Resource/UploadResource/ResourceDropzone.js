import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useDropzone } from "react-dropzone";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import clsx from "clsx";

import { toast } from "@component/ToastProvider";
import { useResource } from "@app/pages/Resource";
import AntButton from "@component/AntButton";
import FileProgress from "@component/FileProgress";

import { BUTTON, CONSTANT, UPLOAD_STATUS } from "@constant";
import { RESOURCE_CONFIG } from "@app/pages/Resource/resourceCommon";
import { uploadResource } from "@services/Resource";
import { cloneObj, humanFileSize, randomKey } from "@common/functionCommons";

import Upload from "@component/SvgIcons/Upload";

const FILE_MAX_SIZE = 50 * 1024 * 1024;

function ResourceDropzone({ user, ...props }) {
  const { t } = useTranslation();
  
  const { setMyResourceData, setOrgResourceData } = useResource();
  const { categorySelected, resourceTypeActive, isShowUpload, setShowUpload } = useResource();
  const { usedCapacity, capacityLimit, getCapacityData } = useResource();
  
  const [resourceFiles, setResourceFiles] = useState([]);
  const [isUploading, setUploading] = useState(false);
  
  
  useEffect(() => {
    if (!isShowUpload) {
      setResourceFiles([]);
      setUploading(false);
    }
  }, [isShowUpload]);
  
  useEffect(() => {
    if (isUploading) {
      const checkStillUpload = resourceFiles.find(resource => resource.status === UPLOAD_STATUS.UPLOADING);
      if (!checkStillUpload) {
        setUploading(false);
        getCapacityData();
      }
    }
  }, [resourceFiles]);
  
  function removeFileItem(fileRemove) {
    setResourceFiles(prevState => prevState.filter(file => file.key !== fileRemove.key));
  }
  
  async function handleUploadResource(resourceFiles) {
    const fileInfo = { type: resourceTypeActive };
    if (categorySelected === CONSTANT.MY_RESOURCE) {
      fileInfo.userId = user._id;
    } else if (categorySelected === CONSTANT.ORG_RESOURCE) {
      fileInfo.organizationId = user.organizationId?._id;
    }
    
    
    const axiosConfig = {
      onUploadProgress: function (progressEvent) {
        let percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        setResourceFiles(prevState => {
          const newState = [...prevState];
          newState.forEach(state => {
            if (state.key === resourceFiles.key) {
              state.status = UPLOAD_STATUS.UPLOADING;
              state.percent = Math.min(percentCompleted, 99);
            }
          });
          return newState;
        });
        
      },
    };
    
    const apiResponse = await uploadResource(resourceFiles.file, fileInfo, axiosConfig);
    setResourceFiles(prevState => {
      const newState = [...prevState];
      newState.forEach(state => {
        if (state.key === resourceFiles.key) {
          state.status = apiResponse.success ? UPLOAD_STATUS.SUCCESS : UPLOAD_STATUS.ERROR;
          state.percent = apiResponse.success ? 100 : 0;
        }
      });
      return newState;
    });
    
    if (apiResponse?.success) {
      if (categorySelected === CONSTANT.MY_RESOURCE) {
        setMyResourceData(prevState => {
          const newState = cloneObj(prevState);
          newState[resourceTypeActive] ||= [];
          newState[resourceTypeActive].push(apiResponse.data);
          return newState;
        });
      } else if (categorySelected === CONSTANT.ORG_RESOURCE) {
        setOrgResourceData(prevState => {
          const newState = cloneObj(prevState);
          newState[resourceTypeActive] ||= [];
          newState[resourceTypeActive].push(apiResponse.data);
          return newState;
        });
      }
    }
  }
  
  async function handleSaveResource() {
    setUploading(true);
    const fileInfo = { type: resourceTypeActive };
    if (categorySelected === CONSTANT.MY_RESOURCE) {
      fileInfo.userId = user._id;
    } else if (categorySelected === CONSTANT.ORG_RESOURCE) {
      fileInfo.organizationId = user.organizationId?._id;
    }
    
    resourceFiles.forEach((resourceFile, index) => {
      if (![UPLOAD_STATUS.UPLOADING, UPLOAD_STATUS.SUCCESS].includes(resourceFile.status)) {
        handleUploadResource(resourceFile);
      }
    });
  }
  
  const { getRootProps, getInputProps, open, fileRejections } = useDropzone({
    onDrop: handleDrop, noClick: true, accept: RESOURCE_CONFIG[resourceTypeActive]?.accept,
    ...resourceTypeActive === CONSTANT.DOCUMENT ? { maxSize: FILE_MAX_SIZE } : {},
    //disabled,
  });
  
  useEffect(() => {
    const isExceededSize = fileRejections.find((fileError) => fileError?.file?.size > FILE_MAX_SIZE);
    if (isExceededSize) {
      toast.error(t("FILE_EXCEEDS_MAX_FILE_SIZE"));
    }
  }, [fileRejections.length]);
  
  const handlePaste = useCallback(async (event) => {
    const items = (event.clipboardData || event.originalEvent.clipboardData).items;
    handleDrop(items);
  }, []);
  
  
  const [allSelectedCapacity, notUploadedCapacity] = useMemo(() => {
    let allSize = 0, notUploadSize = 0;
    resourceFiles.forEach((resource, index) => {
      allSize += resource.file.size;
      if (resource.status !== UPLOAD_STATUS.SUCCESS) {
        notUploadSize += resource.file.size;
      }
    });
    return [allSize, notUploadSize];
  }, [resourceFiles]);
  
  const isCapacityExceeded = useMemo(() => {
    const capacityEstimate = usedCapacity + notUploadedCapacity / 1024 / 1024;
    return capacityEstimate > capacityLimit;
  }, [usedCapacity, allSelectedCapacity, notUploadedCapacity]);
  
  useEffect(() => {
    if (isCapacityExceeded && isShowUpload && !!notUploadedCapacity) {
      toast.error("EXCEEDED_PACKAGE_CAPACITY");
    }
  }, [isCapacityExceeded]);
  
  function handleDrop(files) {
    setResourceFiles(prevState => {
      const newState = files.map(file => ({
        key: randomKey(), file, status: UPLOAD_STATUS.PENDING, percent: 0,
      }));
      return [...prevState, ...newState];
    });
  }
  
  const isDisableUpload = useMemo(() => {
    if (isCapacityExceeded) return true;
    return !resourceFiles.filter(file => ![UPLOAD_STATUS.UPLOADING, UPLOAD_STATUS.SUCCESS].includes(file.status))?.length;
  }, [resourceFiles, isCapacityExceeded]);
  
  return <div {...getRootProps()} onPaste={handlePaste}>
    <input {...getInputProps()} />
    
    <div className="upload-resource__progress-upload">
      <div>
        <AntButton
          size="large"
          type={BUTTON.DEEP_NAVY}
          onClick={open}
          icon={<Upload />}
          iconLocation={CONSTANT.RIGHT}
        >
          {t("SELECT_" + resourceTypeActive)}
        </AntButton>
      </div>
      
      {!!resourceFiles.length && <div className="upload-resource__progress-list">
        {resourceFiles.map((resource, index) => {
          return <FileProgress
            key={index}
            resource={resource}
            onDelete={removeFileItem}
          />;
        })}
      </div>}
      
      {!!allSelectedCapacity && <div className="upload-resource__capacity">
        <div className="upload-resource__capacity-label">
          {t("TOTAL_CAPACITY")}
        </div>
        <div
          className={clsx("upload-resource__capacity-value", { "upload-resource__capacity-exceeded": isCapacityExceeded })}>
          {humanFileSize(allSelectedCapacity)}
        </div>
      </div>}
      <div className="upload-resource__progress-action">
        <AntButton
          size="large"
          onClick={() => setShowUpload(false)}
        >
          {t("CANCEL")}
        </AntButton>
        <AntButton
          size="large"
          type={BUTTON.DEEP_NAVY}
          onClick={handleSaveResource}
          disabled={isDisableUpload}
        >
          {t("UPLOAD")}
        </AntButton>
      </div>
    </div>
  
  </div>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(ResourceDropzone);