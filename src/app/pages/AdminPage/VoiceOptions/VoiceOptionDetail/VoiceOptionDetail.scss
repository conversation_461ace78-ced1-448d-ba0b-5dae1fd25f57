.voice-option-detail {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .voice-option-detail__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--white);
    padding: 24px;
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);

    .voice-option-detail__title {
      h1 {
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 8px;
        color: var(--typo-colours-primary-black);
      }

      .voice-option-detail__description {
        color: var(--typo-colours-support-grey-light);
        font-size: 14px;
        margin: 0;
      }
    }

    .voice-option-detail__actions {
      display: flex;
      gap: 16px;
    }

    @media screen and (max-width: 767.98px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .voice-option-detail__actions {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }

  .voice-option-detail__content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .voice-option-detail__section {
    background-color: var(--white);
    padding: 24px;
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);

    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid var(--background-light-background-grey);
    }
  }

  .voice-option-form {
    width: 100%;

    .form-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 24px;
    }
  }

  .voice-option-detail__audio {
    .voice-upload-container {
      width: 100%;
    }

    .voice-upload-dropzone {
      border: 2px dashed var(--background-light-background-grey);
      border-radius: 8px;
      padding: 24px;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        border-color: var(--primary-colours-blue-navy);
      }
    }

    .voice-upload-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 16px;
      padding: 32px 0;

      .voice-upload-icon {
        font-size: 48px;
        color: var(--primary-colours-blue-navy-light-2);
      }

      .voice-upload-text {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        text-align: center;

        p {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
        }

        span {
          color: var(--typo-colours-support-grey-light);
        }
      }

      .voice-upload-formats {
        color: var(--typo-colours-support-grey-light);
        font-size: 14px;
      }
    }

    .voice-file-preview {
      display: flex;
      flex-direction: column;
      gap: 24px;

      .voice-file-info {
        display: flex;
        align-items: center;
        gap: 16px;

        .voice-file-icon {
          font-size: 32px;
          color: var(--primary-colours-blue-navy);
        }

        .voice-file-details {
          .voice-file-name {
            font-weight: 500;
            font-size: 16px;
          }
        }
      }

      .voice-file-actions {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .voice-audio-player {
          width: 100%;
          border-radius: 8px;
        }

        .voice-file-buttons {
          display: flex;
          justify-content: flex-end;
        }
      }
    }
  }

  .hidden {
    display: none;
  }


}