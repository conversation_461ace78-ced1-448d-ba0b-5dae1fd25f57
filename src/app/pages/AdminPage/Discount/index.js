import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { connect } from "react-redux";
import { Card, Col, Form, Input, Row, Select, Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import { CheckOutlined, EditOutlined, PlusOutlined, SearchOutlined } from "@ant-design/icons";

import Loading from "@src/app/component/Loading";
import TableAdmin from "@src/app/component/TableAdmin";
import AntButton from "@src/app/component/AntButton";
import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import { toast } from "@src/app/component/ToastProvider";
import { paginationConfig, handleSearchParams, formatDate, handleReplaceUrlSearch } from "@src/common/functionCommons";

import { BUTTON, PAGINATION_INIT } from "@constant";
import { handlePagingData } from "@src/common/dataConverter";

import { getAllDiscount, createDiscount, updateDiscount, deleteDiscount } from "@src/app/services/Discount";
import DiscountDetailModal from "./DiscountDetailModal";
import { AntForm } from "@src/app/component/AntForm";
import { confirm } from "@component/ConfirmProvider";

import "./Discount.scss";


function Discount() {
  const location = useLocation();
  const [form] = Form.useForm();
  const { t, i18n } = useTranslation();

  const [discountData, setDiscountData] = useState(PAGINATION_INIT);
  const [modalState, setModalState] = useState({
    open: false,
    discount: null,
  });

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const { query, paging } = handleSearchParams(location.search);
    getDiscountData(query, paging);
    form.setFieldsValue(query);
  }, [location.search]);

  const getDiscountData = async (query, paging) => {
    setIsLoading(true);
    const apiResponse = await getAllDiscount(paging, query);
    if (apiResponse) {
      setDiscountData(handlePagingData(apiResponse, query));
    }
    setIsLoading(false);
  };

  const handleDelete = async (id, code) => {
    confirm.delete({
      title: t("DELETE_DISCOUNT"),
      content: t("DELETE_DISCOUNT_CONFIRM", { code }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async () => {
        setIsLoading(true);
        const apiResponse = await deleteDiscount(id);
        if (apiResponse) {
          toast.success(t("DELETE_DISCOUNT_SUCCESS"));
          getDiscountData(discountData.query, discountData.paging);
        } else {
          toast.error(t("DELETE_DISCOUNT_ERROR"));
          setIsLoading(false);
        }
      },
    });
  }

  const onToggleModal = (discount) => {
    setModalState(pre => ({ open: !pre.open, discount }));
  };

  async function onSave(values) {
    setIsLoading(true);
    const isUpdate = Boolean(modalState.discount);
    const dataRequest = {
      ...values,
      type: values.type.toLowerCase(),
      ...(isUpdate && { _id: modalState.discount._id })
    };
    const dataResponse = isUpdate ? await updateDiscount(dataRequest) : await createDiscount(dataRequest);

    if (dataResponse) {
      toast.success(t(isUpdate ? "UPDATE_DISCOUNT_SUCCESS" : "CREATE_DISCOUNT_SUCCESS"));
      await getDiscountData(discountData.query, discountData.paging);
      onToggleModal();
    } else {
      toast.error(t(isUpdate ? "UPDATE_DISCOUNT_ERROR" : "CREATE_DISCOUNT_ERROR"));
      setIsLoading(false);
    }
  }

  const columns = [
    {
      title: t("CODE"),
      dataIndex: "code",
      width: 250,
      render: (text) => <span className="discount-code-value">{text}</span>,
    },
    {
      title: t("DISCOUNT_LEVEL"),
      dataIndex: "discount",
      width: 150,
    },
    {
      title: t("TYPE"),
      dataIndex: "type",
      width: 150,
      render: value => t(value.toUpperCase())
    },
    {
      title: t("IS_ACTIVATE"),
      dataIndex: "isActivate",
      align: "center",
      width: 150,
      render: (value) => (value && <CheckOutlined />),
    },
    {
      title: t("START_DATE").slice(0, -1),
      dataIndex: "startDate",
      key: "startDate",
      align: "center",
      render: formatDate,
      width: 150,
    },
    {
      title: t("END_DATE").slice(0, -1),
      dataIndex: "endDate",
      key: "endDate",
      align: "center",
      render: formatDate,
      width: 150,
    },
    {
      title: t("NUMBER_OF_USED"),
      dataIndex: "numOfUsed",
      key: "numOfUsed",
      align: "center",
      width: 150,
    },
    {
      title: t("LIMIT"),
      dataIndex: "limit",
      key: "limit",
      align: "center",
      width: 150,
    },
    {
      title: t("ACTION"),
      align: "center",
      width: 120,
      render: (_, record) => (
        <div className="discount-actions">
          <Tooltip title={t("EDIT_DISCOUNT")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-edit-discount"}
              icon={<EditOutlined />}
              onClick={() => onToggleModal(record)}
            />
          </Tooltip>
          <Tooltip title={t("DELETE_DISCOUNT")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-delete-discount"}
              icon={<DeleteIcon />}
              onClick={() => handleDelete(record._id, record.code)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  const pagination = paginationConfig(discountData.paging, discountData.query, i18n.language);

  const submitFormFilter = (values) => {
    handleReplaceUrlSearch(1, discountData.paging.pageSize, values);
  };

  const onClearFilter = () => {
    form.resetFields();
    submitFormFilter({});
  };

  return (
    <Loading active={isLoading} transparent>
      <div className="discount-container">
        <Card className="discount-info-card">
          <div className="discount-info-header">
            <div>
              <h1 className="discount-title">{t("DISCOUNT_MANAGEMENT")}</h1>
              <p className="discount-description">{t("DISCOUNT_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <AntButton
              type={BUTTON.DEEP_NAVY}
              size={"large"}
              className="btn-create-discount"
              icon={<PlusOutlined />}
              onClick={() => onToggleModal()}
            >
              {t("CREATE_DISCOUNT")}
            </AntButton>
          </div>
        </Card>

        <Card className="discount-search-card">
          <AntForm form={form} layout="horizontal" size={"large"} className="form-filter" onFinish={submitFormFilter}>
            <Row gutter={24} align="middle">
              <Col xs={24} md={8} lg={8}>
                <AntForm.Item name="code" className="search-form-item">
                  <Input
                    placeholder={t("SEARCH_DISCOUNT_CODE_PLACEHOLDER")}
                    allowClear
                    prefix={<SearchOutlined />}
                  />
                </AntForm.Item>
              </Col>
              <Col xs={24} md={8} lg={8}>
                <AntForm.Item name="isActivate" className="search-form-item">
                  <Select placeholder={t("FILTER_BY_STATUS")} allowClear>
                    <Select.Option value="true">{t("ACTIVE")}</Select.Option>
                    <Select.Option value="false">{t("INACTIVE")}</Select.Option>
                  </Select>
                </AntForm.Item>
              </Col>
              <Col xs={24} md={8} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton size={"large"} type={BUTTON.GHOST_WHITE} onClick={onClearFilter}>
                    {t("CLEAR")}
                  </AntButton>
                  <AntButton size={"large"} htmlType={"submit"} type={BUTTON.DEEP_NAVY}>
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="discount-table-card">
          <TableAdmin
            columns={columns}
            dataSource={discountData.rows}
            scroll={{ x: 1000 }}
            pagination={pagination}
            rowKey="_id"
            className="discount-table"
            rowClassName={() => "discount-table-row"}
            locale={{ emptyText: t("NO_DISCOUNTS_FOUND") }}
          />
        </Card>

        <DiscountDetailModal {...modalState} onCancel={onToggleModal} onFinish={onSave} />
      </div>
    </Loading>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(Discount);
