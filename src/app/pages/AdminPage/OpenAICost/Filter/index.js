import React, { useEffect, useState } from "react";
import { Col, Form, Row, Select, DatePicker } from "antd";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { CalendarOutlined, SearchOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';
dayjs.extend(weekday);
dayjs.extend(localeData);

import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";

import { BUTTON } from "@constant";

import "./Filter.scss";

const { RangePicker } = DatePicker;

const Filter = (props) => {
  const { extraFilter, allowClear } = props;
  const [formFilter] = Form.useForm();
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  const [isShowSelectDate, setShowSelectDate] = useState(false);
  const [minDate, setMinDate] = useState(null);
  const [maxDate, setMaxDate] = useState(null);
  const [isSearchable, setSearchable] = useState(false);

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const query = Object.fromEntries(queryParams.entries());
    handleQueryFromUrl(query);
  }, [location.search]);

  const handleQueryFromUrl = (query) => {
    const { time, fromDate, toDate } = query;

    const newQuery = {
      ...query,
      ...fromDate ? { fromDate: dayjs(fromDate * 1000) } : {},
      ...toDate ? { toDate: dayjs(toDate * 1000) } : {},
    };

    setShowSelectDate(time === "custom");
    setMinDate(newQuery?.fromDate);
    setMaxDate(newQuery?.toDate);
    formFilter.setFieldsValue(newQuery);
  };

  const onFinish = (values) => {
    const { time, fromDate, toDate, ...rest } = values;
    const query = {
      ...rest,
      time,
      ...time === "custom" ? {
        fromDate: fromDate ? Math.floor(fromDate.startOf("day").valueOf() / 1000) : undefined,
        toDate: toDate ? Math.floor(toDate.endOf("day").valueOf() / 1000) : undefined,
      } : {},
    };

    const searchParams = new URLSearchParams();
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== "") {
        searchParams.append(key, value);
      }
    });

    navigate(`${location.pathname}?${searchParams.toString()}`);
  };

  const onValuesChange = (changedValues, allValues) => {
    if (changedValues.time) {
      setShowSelectDate(changedValues.time === "custom");
      if (changedValues.time !== "custom") {
        formFilter.setFieldsValue({ fromDate: undefined, toDate: undefined });
      }
    }

    const hasValues = Object.values(allValues).some(val => val !== undefined && val !== "");
    setSearchable(hasValues);
  };

  const clearFormFilter = () => {
    formFilter.resetFields();
    setShowSelectDate(false);
    setSearchable(false);
    navigate(location.pathname);
  };

  return (
    <AntForm
      form={formFilter}
      layout="horizontal"
      size="large"
      className="filter-form-openai-cost"
      onFinish={onFinish}
      onValuesChange={onValuesChange}
    >
      <Row gutter={16} align="middle" className="filter-row">
        <Col flex="auto">
          <Row gutter={16} wrap={false} className="filter-inputs-row">
            <Col xs={24} md={12} lg={6}>
              <AntForm.Item name="time" className="search-form-item time-select">
                <Select
                  placeholder={t("SELECT_TIME_PERIOD")}
                  allowClear={allowClear}
                  options={[
                    { value: "week", label: t("THIS_WEEK") },
                    { value: "month", label: t("THIS_MONTH") },
                    { value: "custom", label: t("CUSTOM") },
                  ]}
                  suffixIcon={<CalendarOutlined />}
                />
              </AntForm.Item>
            </Col>

            {isShowSelectDate && (
              <>
                <Col xs={24} md={12} lg={6}>
                  <AntForm.Item
                    name="fromDate"
                    className="search-form-item date-picker-item"
                    rules={[
                      {
                        validator: (_, value) => {
                          if (isShowSelectDate && !value) {
                            return Promise.reject(t("SELECT_FROM_DATE"));
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <DatePicker
                      placeholder={t("FROM_DATE")}
                      format="DD/MM/YYYY"
                      allowClear
                      disabledDate={(current) => {
                        return current && current > dayjs().endOf('day');
                      }}
                      onChange={setMinDate}
                    />
                  </AntForm.Item>
                </Col>
                <Col xs={24} md={12} lg={6}>
                  <AntForm.Item
                    name="toDate"
                    className="search-form-item date-picker-item"
                    rules={[
                      {
                        validator: (_, value) => {
                          if (isShowSelectDate && !value) {
                            return Promise.reject(t("SELECT_TO_DATE"));
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <DatePicker
                      placeholder={t("TO_DATE")}
                      format="DD/MM/YYYY"
                      allowClear
                      disabledDate={(current) => {
                        return current && current > dayjs().endOf('day');
                      }}
                      onChange={setMaxDate}
                    />
                  </AntForm.Item>
                </Col>
              </>
            )}

            {extraFilter?.map((filter, index) => (
              <Col key={index}>
                <AntForm.Item name={filter.name} className="search-form-item">
                  <div className="component-wrapper">
                    {filter.component}
                  </div>
                </AntForm.Item>
              </Col>
            ))}
          </Row>
        </Col>
        <Col>
          <div className="search-buttons">
            <AntButton type={BUTTON.GHOST_WHITE} size="large" onClick={clearFormFilter}>{t("CLEAR")}</AntButton>
            <AntButton
              type={BUTTON.DEEP_NAVY}
              size="large"
              htmlType="submit"
              disabled={!isSearchable}
            >
              {t("SEARCH")}
            </AntButton>
          </div>
        </Col>
      </Row>
    </AntForm>
  );
};

export default Filter;
