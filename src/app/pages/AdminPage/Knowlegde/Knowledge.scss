@import "src/app/styles/scroll";

.knowledges {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  .knowledges-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .btn-create-knowledges {
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      gap: 8px;
      padding: 10px 24px !important;
    }
  }

  .knowledge-table {
    .row-selected {
      .ant-table-cell {
        background-color: var(--primary-colours-blue-navy-light-2);
      }
    }

    td {
      &.ant-table-cell {
        padding: 0 24px !important;
      }
    }

    .ant-table-cell {
      &:has(.knowledge-content) {
        background-color: unset !important;
        padding: 0 !important;
      }

      &:not(:has(.knowledge-content)) {
        cursor: pointer;
      }
    }

    .knowledge-content-title {
      display: flex;
      gap: 24px;
      justify-content: space-between;

      .knowledge-content-action {
        display: flex;
        gap: 16px;
        justify-content: center;
      }
    }

    .knowledge-content {
      @extend .scrollbar;
      @extend .scrollbar-show;

      white-space: pre-line;
      padding: 16px 24px;
      overflow-y: auto;

      @for $i from 1 through 50 {
        &.rows-height-#{$i} {
          height: if($i > 10, #{$i*53}px, 530px);
        }
      }
    }

    &:has(.knowledge-content:hover) {
      .ant-table-row {
        &:not(&.row-selected) {
          .ant-table-cell {
            background-color: unset !important;
          }
        }
      }
    }
  }

  .ant-pagination-total-text {
    height: 40px !important;
    display: flex;
    align-items: center;
    margin-inline-end: 16px !important;
  }

  .ant-pagination-options {
    .ant-select {
      height: 40px;
    }
  }

  .ant-pagination-item-link {
    background-color: var(--background-light-background-2) !important;
    border: var(--background-light-background-grey) !important;
    border-radius: 8px !important;
    color: var(--typo-colours-support-blue-light) !important;
    width: 40px !important;
    height: 40px !important;
  }

  .ant-pagination-item {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: var(--background-light-background-2) !important;
    border: var(--background-light-background-grey) !important;
    border-radius: 8px !important;
    width: 40px !important;
    height: 40px !important;

    a {
      color: var(--typo-colours-support-blue-light) !important;
    }
  }

  .ant-pagination-item-active {
    background-color: var(--primary-colours-blue) !important;

    a {
      color: var(--background-light-background-2) !important;
    }
  }

  .ant-form-item {
    margin: 0;
  }
}

.form-filter__content {
  width: 100% !important;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 24px;
  margin-right: 24px;

  .search-button {
    display: flex;
    gap: 24px;
  }
}