import { useEffect, useState } from "react";
import AntModal from "@src/app/component/AntModal";
import { Col, Row } from "antd";

import AntButton from "@src/app/component/AntButton";

import Copy from "@component/SvgIcons/Copy";
import CheckIcon from "@src/app/component/SvgIcons/CheckIcon";

import { BUTTON } from "@constant";

import './PreviewPromptInstruction.scss';

const PreviewPromptInstruction = ({ ...props }) => {
  const { visible, promptData, onCancel } = props;

  const [isCopied, setCopied] = useState(false);

  useEffect(() => {
    if (isCopied) {
      setTimeout(() => {
        setCopied(false);
      }, 1000);
    }
  }, [isCopied]);

  const onCopy = () => {
    if (!isCopied) {
      const copyText = promptData.map(item => item.content).join('\n');
      navigator.clipboard.writeText(copyText);
      setCopied(true);
    }
  }
  return (
    <AntModal
      open={visible}
      title="Preview prompt"
      onCancel={onCancel}
      footer={null}
      width={1200}
      className="preview-prompt-instruction"
    >
      <div className="prompt-wrapper">
        {promptData.map((item, index) => (
          <Row key={index}>
            <Col span={3} className="prompt-role">
              {item.role}
            </Col>
            <Col span={21} className="prompt-content">
              {item.content}
            </Col>
          </Row>
        ))}
      </div>
      {!!promptData.length && <AntButton
        type={BUTTON.DEEP_BLUE}
        onClick={onCopy}
        className={"copy-button"}
        icon={isCopied ? <CheckIcon /> : <Copy />}
      >
        {isCopied ? "Copied" : "Copy"}
      </AntButton>}
    </AntModal>
  );
};
export default PreviewPromptInstruction;