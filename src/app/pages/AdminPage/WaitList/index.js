import React, { useEffect, useMemo, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { connect } from "react-redux";
import { Card, Input, Tooltip, Form, Row, Col } from "antd";
import { useTranslation } from "react-i18next";
import { ArrowRightOutlined, SearchOutlined } from "@ant-design/icons";

import TimeFilter from "@src/app/component/TimeFilter";
import Loading from "@src/app/component/Loading";
import TableAdmin from "@src/app/component/TableAdmin";
import AntButton from "@src/app/component/AntButton";
import { AntForm } from "@src/app/component/AntForm";

import { toast } from "@src/app/component/ToastProvider";
import { getWaitListPagination, moveToWhiteList } from "@services/WaitList";
import { formatTimeDate, orderColumn, paginationConfig, convertQueryToObject, handleReplaceUrlSearch } from "@src/common/functionCommons";

import { BUTTON, PAGINATION_INIT } from "@constant";
import { handlePagingData } from "@src/common/dataConverter";

import "./WaitList.scss";


function WaitList() {
  const location = useLocation();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const [waitListData, setWaitListData] = useState(PAGINATION_INIT);
  const [formFilter] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);

  const queryParams = useMemo(() => convertQueryToObject(location.search), [location.search]);

  useEffect(() => {
    const queryParams = convertQueryToObject(location.search)
    getWaitListData(queryParams);
  }, [queryParams]);

  const getWaitListData = async (query) => {
    setIsLoading(true);
    const apiResponse = await getWaitListPagination(query);
    if (apiResponse) {
      setWaitListData(handlePagingData(apiResponse, query));
    }
    setIsLoading(false);
  };

  const onMoveToWhiteList = async (recordId) => {
    setIsLoading(true);
    const apiResponse = await moveToWhiteList(recordId);
    if (apiResponse) {
      if (waitListData.rows.length === 1 && waitListData.paging.page > 1) {
        const { page, pageSize } = waitListData.paging;
        handleReplaceUrlSearch(page - 1, pageSize, waitListData.query);
      } else {
        const updateWaitListData = await getWaitListPagination(queryParams);
        if (updateWaitListData) {
          setWaitListData(handlePagingData(updateWaitListData, queryParams));
        }
      }
      toast.success(t("MOVE_TO_WHITELIST_SUCCESS"));
    } else {
      toast.error(t("MOVE_TO_WHITELIST_ERROR"));
    }
    setIsLoading(false);
  }

  const columns = [
    orderColumn(waitListData.paging),
    {
      title: t("EMAIL"),
      dataIndex: "email",
      width: 400,
    },
    {
      title: t("FULL_NAME"),
      dataIndex: "fullName",
      width: 400,
    },
    {
      title: t("CREATED_AT"),
      dataIndex: "createdAt",
      key: "createdAt",
      align: "center",
      render: formatTimeDate,
      width: 150,
    },
    {
      title: t("ACTION"),
      align: "center",
      render: (value, record) =>
        <div className="wait-list-actions">
          <Tooltip title={t("MOVE_TO_WHITELIST")}>
            <AntButton
              type={BUTTON.GHOST_BLUE}
              size="small"
              icon={<ArrowRightOutlined />}
              onClick={() => onMoveToWhiteList(record?._id)} />
          </Tooltip>
        </div>,
      width: 100,
    },
  ];

  const pagination = paginationConfig(waitListData.paging, waitListData.query, i18n.language);

  const onSubmitFilter = (values) => {
    handleReplaceUrlSearch(1, waitListData.paging.pageSize, values);
  };

  const onClearFilter = () => {
    formFilter.resetFields();
    handleReplaceUrlSearch(1, waitListData.paging.pageSize, {});
  };

  return (
    <Loading active={isLoading} transparent>
      <div className="wait-list-container">
        <Card className="wait-list-info-card">
          <div className="wait-list-info-header">
            <div>
              <h1 className="wait-list-title">{t("WAIT_LIST")}</h1>
              <p className="wait-list-description">{t("WAIT_LIST_DESCRIPTION")}</p>
            </div>
          </div>
        </Card>

        <Card className="wait-list-search-card">
          <AntForm onFinish={onSubmitFilter} layout="horizontal" form={formFilter} className="form-filter" size={"large"}>
            <Row gutter={24} align="middle">
              <Col xs={24} md={8} lg={8}>
                <AntForm.Item name="email" className="search-form-item">
                  <Input
                    placeholder={t("SEARCH_EMAIL_PLACEHOLDER")}
                    allowClear
                    prefix={<SearchOutlined />}
                  />
                </AntForm.Item>
              </Col>
              <Col xs={24} md={8} lg={8}>
                <AntForm.Item name="fullName" className="search-form-item">
                  <Input
                    placeholder={t("SEARCH_FULL_NAME_PLACEHOLDER")}
                    allowClear
                    prefix={<SearchOutlined />}
                  />
                </AntForm.Item>
              </Col>
              <Col xs={24} md={8} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton type={BUTTON.GHOST_WHITE} size="large" onClick={onClearFilter}>
                    {t("CLEAR")}
                  </AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} size="large" htmlType="submit">
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="wait-list-table-card">
          <TableAdmin
            columns={columns}
            dataSource={waitListData.rows}
            scroll={{ x: 1000 }}
            pagination={pagination}
            rowKey="_id"
            className="wait-list-table"
            rowClassName={() => "wait-list-table-row"}
            locale={{ emptyText: t("NO_WAIT_LIST_FOUND") }}
          />
        </Card>
      </div>
    </Loading>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(WaitList);
