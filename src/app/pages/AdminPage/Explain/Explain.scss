.explain-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles
  .explain-info-card,
  .explain-search-card,
  .explain-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // Header styles
  .explain-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .explain-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--typo-colours-primary-black);
    }

    .explain-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }

    .btn-create-explain {
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      gap: 8px;
      padding: 10px 24px !important;
    }
  }

  // Search form styles
  .search-form-item {
    margin-bottom: 0;
  }

  .search-buttons-col {
    display: flex;
    justify-content: flex-end;
  }

  .search-buttons {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
  }

  .form-filter {
    width: 100%;
  }

  .ant-form-item {
    margin: 0;
  }

  .explain-name-value {
    font-weight: 600;
    color: var(--typo-colours-primary-black);
    font-size: 14px;
  }

  // Tag styles
  .ant-tag {
    padding: 2px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 80px;
  }

  // Table styles
  .explain-table {
    width: 100%;

    .ant-table-container {
      overflow-x: auto;
    }

    .ant-table-thead > tr > th {
      background-color: var(--background-light-background-1);
      font-weight: 600;
      color: var(--typo-colours-primary-black);
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: top;
      white-space: normal;
      word-break: break-word;
    }

    .explain-table-row {
      &:hover {
        background-color: var(--background-light-background-2);
      }
    }

    .explain-actions {
      display: flex;
      flex-direction: row;
      justify-content: center;
      gap: 8px;

      .btn-edit-explain,
      .btn-delete-explain {
        &:hover {
          background: var(--background-hover);
        }
      }
    }
  }

  // Responsive styles
  @media (max-width: 768px) {
    .explain-info-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .btn-create-explain {
        width: 100%;
        justify-content: center;
      }
    }

    .form-filter {
      .search-buttons {
        margin-top: 16px;
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}

.explain-detail-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  background-color: var(--background-light-background-2);
  border-radius: 8px;
  font-family: Segoe UI;

  .explain-detail-header {
    &__title {
      font-weight: 700;
      font-size: 16px;
      line-height: 20px;
      color: var(--typo-colours-primary-black);
    }
  }

  .explain-form {
    gap: 24px;
  }

  .ant-form-item-label {
    label {
      height: unset !important;
    }
  }

  .schema-json-editor {
    font-family: monospace;
    font-size: 14px;
    background-color: #f5f5f5;
  }

  .form-actions-submit {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 32px;
    margin-top: 24px;
  }
}
