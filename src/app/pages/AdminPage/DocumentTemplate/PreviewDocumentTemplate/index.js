import React, { useEffect, useState, useRef } from "react";
import { useTranslation } from "react-i18next";

import Loading from "@component/Loading";
import AntModal from "@component/AntModal";
import PreviewPdf from "@component/PreviewPdf";

import { createFileOrgTemplate, previewDocTemplateFile } from "@services/DocumentTemplate";
import axios from "axios";

import "./PreviewDocumentTemplate.scss";

function PreviewDocumentTemplate({ isOpen, handleCancel, statePreviewPdf }) {
  const { t } = useTranslation();

  const [isLoading, setLoading] = useState(false);
  const [previewData, setPreviewData] = useState(null);
  const cancelTokenRef = useRef(null);

  useEffect(() => {
    if (isOpen && statePreviewPdf?.idPreview) {
      setLoading(true);
      handlePreviewDocumentTemplate(statePreviewPdf.idPreview);
    } else {
      setPreviewData(null);
    }

    return () => {
      // Cancel any pending requests when component unmounts or when modal closes
      if (cancelTokenRef.current) {
        cancelTokenRef.current.cancel('Operation canceled due to component unmount');
      }
    };
  }, [isOpen, statePreviewPdf?.idPreview]);

  async function handlePreviewDocumentTemplate(id) {
    try {
      // Cancel previous request if exists
      if (cancelTokenRef.current) {
        cancelTokenRef.current.cancel('Operation canceled due to new request');
      }

      // Create a new cancel token
      cancelTokenRef.current = axios.CancelToken.source();
      const config = { cancelToken: cancelTokenRef.current.token };

      const apiRequest = { templateId: id };
      const apiResponse = await createFileOrgTemplate(apiRequest, config);

      if (apiResponse?.fileName) {
        const previewResponse = await previewDocTemplateFile(apiResponse.fileName, config);
        if (previewResponse) {
          setPreviewData(previewResponse);
        }
      }
    } catch (error) {
      if (!axios.isCancel(error)) {
        console.error('Error previewing document template:', error);
      }
    } finally {
      setLoading(false);
    }
  }

  return <>
    <AntModal
      width={1056}
      formId="preview-document-template"
      title={t("PREVIEW_DOCUMENT_TEMPLATE")}
      className="preview-document-template-container"
      open={isOpen}
      onCancel={handleCancel}
      footerless
    >
      <Loading active={isLoading}>
        <PreviewPdf file={previewData} />
      </Loading>
    </AntModal>
  </>;
}

export default PreviewDocumentTemplate;
