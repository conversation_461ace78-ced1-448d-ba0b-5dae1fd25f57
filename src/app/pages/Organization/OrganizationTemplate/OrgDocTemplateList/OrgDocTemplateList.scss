.org-doc-template-list {
  overflow-x: scroll;
  display: flex;
  gap: 16px;
  padding: 20px 0 8px 0;
  margin: -20px 0 0 0;
  user-select: none;

  .org-doc-template-item {
    //min-height: 266px;
    position: relative;
    width: 180px;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    padding: 11px;
    border-radius: 8px;
    box-shadow: var(--shadow-level-2);
    border: 1px solid var(--background-light-background-grey);
    cursor: pointer;
    overflow: hidden;

    //@media screen and (min-width: 1024px) and (max-width: 1535.98px) {
    //  width: 100px;
    //}
    //@media screen and (min-width: 768px) and (max-width: 1023.98px) {
    //  width: 100px;
    //}
    //@media screen and (max-width: 767.98px) {
    //  width: 100px;
    //}

    .org-doc-template-item__published {
      z-index: 2;
      width: 100%;
      position: absolute;
      top: 20px;
      right: -58px;
      height: 21px;
      background: #DDFFEF;
      color: #2ABD76;
      font-size: 13px;
      text-transform: uppercase;
      place-content: center;
      display: flex;
      align-items: center;
      transform: rotate(45deg);
    }

    .org-doc-template__preview {
      overflow: hidden;
      aspect-ratio: 156 / 190;
      border-radius: 8px;
      border: 1px solid var(--background-light-background-grey);
      position: relative;

      .org-doc-template__thumbnail-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .org-doc-template__thumbnail-default {
        z-index: 1;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: var(--white);

        img {
          height: 80px;
        }
      }

    }

    .org-doc-template__info {
      margin-top: 14px;
      display: flex;
      flex-direction: row;
      gap: 8px;

      .org-doc-template__info-img {
        height: 24px;
        width: 24px;
        border-radius: 50%;
        overflow: hidden;
      }

      .org-doc-template__info-name {
        flex: 1;
        line-height: 24px;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }

    .org-doc-template__last-used {
      font-size: 10px;
      color: var(--typo-colours-support-blue-light);
      line-height: 14px;
    }

    &:hover,
    &.org-doc-template-item__active {
      border: 1px solid var(--primary-colours-blue-navy)
    }

    &.org-doc-template-item__upload {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      gap: 8px;
      color: var(--primary-colours-blue-navy);
      min-height: 266px;

      svg path {
        stroke: var(--primary-colours-blue-navy);
      }
    }
  }

  &.org-doc-template-list__grid {
    display: grid;
    overflow-x: unset;
    max-height: 100%;
    overflow-y: auto;

    @media screen and (min-width: 1536px) {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }

    @media screen and (min-width: 1300px) and (max-width: 1535.98px)  {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    @media screen and (min-width: 1023.98px) and (max-width: 1299.99px) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    @media screen and (max-width: 1023.98px) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    .org-doc-template-item{
      width: unset !important;
    }
  }
}