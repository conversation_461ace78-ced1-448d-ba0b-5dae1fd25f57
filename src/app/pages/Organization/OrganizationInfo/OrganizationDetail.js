import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import clsx from "clsx";
import { Popover } from "antd";
import { connect } from "react-redux";

import Pie<PERSON>hart from "@src/app/component/PieChart";

import { formatDate } from "@src/common/functionCommons";
import { uploadOrganizationAvatar } from "@services/Organization";
import { toast } from "@src/app/component/ToastProvider";

import { CONSTANT } from "@constant";

import BUILDING_ICON from "@src/asset/icon/building/building.svg";
import { API } from "@api";
import Loading from "@src/app/component/Loading";

import * as authRedux from "@src/ducks/auth.duck";

const OrganizationDetail = ({ user, ...props }) => {
  const { t } = useTranslation();
  const { organizationData, setOrganizationData, numberOfMembers } = props;
  const { organizationUsed } = organizationData;

  const capacityInfo = `${Math.round(organizationUsed?.capacityUsed * 100) / 100} / ${organizationUsed?.capacityLimit} MB`;
  const subscriptionEndDate = organizationData?.subscription?.endDate;
  const isOutOfDate = subscriptionEndDate && new Date(subscriptionEndDate) < new Date();
  const thumbnailFileId = organizationData?.avatarId?.thumbnailFileId;
  const avatarSrc = thumbnailFileId ? API.STREAM_ID.format(thumbnailFileId) : BUILDING_ICON;

  const [isLoading, setLoading] = useState(false);

  const renderNumberOfRun = (value) => {
    return value === CONSTANT.UNLIMITED ? t("UNLIMITED") : `${value} ${t('RUNS')}`
  }

  const onUploadOrganizationAvartar = async (e) => {
    const file = e.target.files[0];
    if (file) {
      const fileType = file.type;
      if (!fileType.startsWith('image/')) {
        toast.error(t("PLEASE_UPLOAD_IMAGES"));
        return;
      }
      setLoading(true);
      const result = await uploadOrganizationAvatar(file, organizationData?._id);
      if (result) {
        setOrganizationData({ ...organizationData, avatarId: result.avatarId });
        props.setUser({ ...user, organizationId: { ...user.organizationId, avatarId: result.avatarId } });
        toast.success(t("UPLOAD_ORG_AVATAR_SUCCESS"));
      }
      setLoading(false);
    }
  }

  const handleClickUploadAvatar = () => {
    document.getElementById('upload-avatar').click();
  }

  return <div className="organization-detail">
    <Loading active={isLoading} className="updload-avartar-loading">
      <Popover content={t("UPLOAD_AVATAR")} trigger="hover" placement="bottomLeft">
        <div className="organization-detail__avatar" onClick={handleClickUploadAvatar}>
          <img src={avatarSrc} alt="" />
          <input type="file" id="upload-avatar" accept='image/*' hidden onChange={onUploadOrganizationAvartar} />
        </div>
      </Popover>
    </Loading>

    <div className="organization-detail__name">{organizationData?.name}</div>
    <div className="organization-detail__info">
      <div className="organization-detail__info-item">
        <div className="organization-detail__info-item-label">{t("USER")}</div>
        <div className="organization-detail__info-item-value">{`${numberOfMembers} ${t("MEMBERS")}`}</div>
      </div>
      <div className="organization-detail__info-item">
        <div className="organization-detail__info-item-label">{t("PACKAGE_NAME")}</div>
        <div>{organizationData?.subscription?.packageId?.name}</div>
      </div>
      <div className="organization-detail__info-item">
        <div className="organization-detail__info-item-label">{t("DATA_CAPACITY")}</div>
        <div>{capacityInfo}</div>
      </div>
    </div>
    <div className="organization-detail__chart">
      <div className="organization-detail__chart-header">
        <div className="chart-header__item chart-header__item-left-side">
          <div className="chart-header__item-title">{t("TEXT_TOOLS")}</div>
          <div className="chart-header__item-value">{renderNumberOfRun(organizationUsed?.textLimit)}</div>
        </div>
        <div className="chart-header__item chart-header__item-right-side">
          <div className="chart-header__item-title">{t("LIMIT_RESET_ON")}</div>
          <div className={clsx("chart-header__item-value", { "out-of-date": isOutOfDate })}>
            {formatDate(subscriptionEndDate)}
          </div>
        </div>
      </div>
      <PieChart total={organizationUsed?.textLimit} usage={organizationUsed?.textUsed} width={100} height={100} />
      <div className="organization-detail__chart-footer">
        {`${renderNumberOfRun(organizationUsed?.textUsed)}/ ${renderNumberOfRun(organizationUsed?.textLimit)}`}
      </div>
    </div>

    <div className="organization-detail__chart">
      <div className="organization-detail__chart-header">
        <div className="chart-header__item chart-header__item-left-side">
          <div className="chart-header__item-title">{t("MEDIA_TOOLS")}</div>
          <div className="chart-header__item-value">{renderNumberOfRun(organizationUsed?.mediaLimit)}</div>
        </div>
        <div className="chart-header__item chart-header__item-right-side">
          <div className="chart-header__item-title">{t("LIMIT_RESET_ON")}</div>
          <div className={clsx("chart-header__item-value", { "out-of-date": isOutOfDate })}>
            {formatDate(subscriptionEndDate)}
          </div>
        </div>
      </div>
      <PieChart total={organizationUsed?.mediaLimit} usage={organizationUsed?.mediaUsed} width={100} height={100} />
      <div className="organization-detail__chart-footer">
        {`${renderNumberOfRun(organizationUsed?.mediaUsed)}/ ${renderNumberOfRun(organizationUsed?.mediaLimit)}`}
      </div>
    </div>

  </div>;
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = {
  ...authRedux.actions,
};
export default connect(mapStateToProps, mapDispatchToProps)(OrganizationDetail);
