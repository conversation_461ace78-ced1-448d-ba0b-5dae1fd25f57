import React, { useCallback, useEffect, useState } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import clsx from "clsx";
import <PERSON>tie from "lottie-react";

import AntButton from "@src/app/component/AntButton";
import SelectPage from "./SelectPage";
import ModalPreviewPdf from "../../ModalPreviewPdf";

import { BUTTON, CONSTANT } from "@constant";

import Loading from "@src/app/component/Loading";

import ChevronLeft from "@component/SvgIcons/ChevronLeft";
import ChevronRight from "@component/SvgIcons/ChevronRight";
import Maximize from "@src/asset/icon/maximize-24.svg";
import ErrorIcon from "@src/asset/icon/error/error-triangle.svg";
import * as loadingAnimation from "@src/asset/animations/blue-spin-loading.json";

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/build/pdf.worker.min.js",
  import.meta.url,
).toString();

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

import './PreviewPdf.scss';

const PreviewPdf = (props) => {
  const { pdfFile } = props;
  const { totalPages, setTotalPages } = props;
  const { pageRange, setPageRange } = props;
  const { validatePageRange } = props;
  const { filesUploadStatus } = props;

  const [currentPage, setCurrentPage] = useState(1);
  const [openPreviewModal, setOpenPreviewModal] = useState(false);

  const [isLoading, setLoading] = useState(false);

  const isUploading = filesUploadStatus?.status === CONSTANT.UPLOADING;
  const isError = filesUploadStatus?.status === CONSTANT.ERROR;
  const isShowBackdrop = pdfFile && !isLoading && !isError;
  const isShowPage = isShowBackdrop && !isUploading;

  useEffect(() => {
    if (pdfFile) {
      setLoading(true);
    }
  }, [pdfFile]);

  const onLoadSuccess = useCallback(({ numPages }) => {
    const { startPage, endPage } = pageRange;
    if (!startPage && !endPage) {
      const pageRangeData = { startPage: 1, endPage: Math.min(2, numPages) };
      setPageRange(pageRangeData);
    }
    setTotalPages(numPages);

    setLoading(false);
  }, [pdfFile]);

  const onLoadError = useCallback((error) => {
    console.log("error", error);
    setLoading(false);
  }, [pdfFile]);

  function handlePrevPage() {
    setCurrentPage(prevState => prevState - 1);
  }

  function handleNextPage() {
    setCurrentPage(prevState => prevState + 1);
  }

  const onPreviewPdfModal = () => {
    if (isUploading) return;
    setOpenPreviewModal(pre => !pre);
  }

  if (!pdfFile) return null;

  return (<Loading active={isLoading} className="writing-preview-pdf" transparent>
    <div className="writing-preview-pdf__content">
      <div className="writing-preview-pdf__document">
        {isError ? <div className="writing-preview-pdf__error">
          <img src={ErrorIcon} alt="" />
        </div>
          : <>
            <Document
              file={pdfFile}
              onLoadSuccess={onLoadSuccess}
              onLoadError={onLoadError}
              loading={null}
            >
              <Page pageNumber={currentPage} loading={null} />
            </Document>
            {isShowBackdrop &&
              <div className={clsx("preview-pdf-backdrop", { "preview-pdf-backdrop-uploading": isUploading })}
                onClick={onPreviewPdfModal} >
                {isUploading
                  ? <>
                    <Lottie
                      className="loading-spin"
                      animationData={loadingAnimation}
                      loop={true}
                    />
                    <span className="loading-percent">{`${filesUploadStatus?.percent}%`}</span>
                  </>
                  : <img src={Maximize} alt="" />}
              </div>}</>}
      </div>

      {isShowPage && <div className="writing-preview-pdf__page-select">
        <AntButton
          size="mini"
          type={BUTTON.GHOST_WHITE}
          icon={<ChevronLeft />}
          onClick={handlePrevPage}
          disabled={currentPage < 2}
        />

        {!!totalPages && `${currentPage} / ${totalPages}`}

        <AntButton
          size="mini"
          type={BUTTON.GHOST_WHITE}
          icon={<ChevronRight />}
          onClick={handleNextPage}
          disabled={currentPage >= totalPages}
        />
      </div>}
    </div>
    {isShowPage && <SelectPage pageRange={pageRange} setPageRange={setPageRange} validatePageRange={validatePageRange} />}
    <ModalPreviewPdf
      openPreview={openPreviewModal}
      onPreviewPdf={onPreviewPdfModal}
      file={pdfFile}
      page={currentPage}
    />
  </Loading>
  );

};

export default PreviewPdf;