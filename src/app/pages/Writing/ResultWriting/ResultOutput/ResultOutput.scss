@import "src/app/styles/scroll";

.result-writing-output {
  display: flex;
  flex-direction: column;
  border-radius: 16px;
  background-color: #FFFFFF;
  box-shadow: 0 10px 16px 0 #00000029;
  max-height: calc(100vh - 245px);
  grid-column: span 2/ span 2;

  @extend .scrollbar;
  @extend .scrollbar-show;

  .result-writing-output__header {
    padding: 16px;
    display: flex;
    flex-direction: row;
    gap: 8px;
    flex-wrap: wrap;
    background-color: #FFFFFF;

    .ant-btn {
      padding-left: 15px;
      padding-right: 15px;
      border-radius: 8px;
    }
  }

  .result-writing-output__body {
    border-top: 1px solid #DBDBDB;

    .result-writing-output__body-content {
      padding: 15px;
      display: flex;
      align-content: baseline;
      flex-direction: row;
      gap: 16px;
      flex-wrap: wrap;
      background: #FAFBFF;
      // background: radial-gradient(14.32% 110px at 30.8% 87px, #DCD7FF 0%, #E1DDFF 0%, #E4E0FF 0%, #FAFBFF 100%);

      .evaluation__content {
        display: flex;
        flex-direction: column;
        gap: 16px;
        width: 100%;

        ul {
          margin-top: 4px;
          margin-bottom: 0;

          li {
            margin-bottom: 4px;
          }
        }

        .evaluation__content__item {
          display: flex;
          flex-direction: column;
          gap: 16px;

          // .html-content {
          p:first-child {
            margin-block-start: 0;
          }

          p:last-child {
            margin-block-end: 0;
          }

          // }

          .content__item__title {
            font-weight: 600;
            font-size: 22px;
            line-height: 30px;
            display: flex;
            gap: 7px;
            align-items: center;

            img {
              width: 24px;
              height: 24px;
            }
          }
        }

        .evaluation-suggest {
          .suggest-item {
            cursor: pointer;
            display: flex;
            flex-direction: column;
            padding: 16px 24px;
            gap: 8px;
            border-radius: 4px;
            background: #E8F3FF;
            border-left: 6px solid #0C93FF;


            .suggest-item__text {
              font-weight: 500;
              line-height: 20.8px;

              .suggest-item__arrow,
              .suggest-item__suggestion {
                color: var(--typo-colours-support-blue);
              }
            }

            .suggest-item__explanation {
              font-size: 16px;
              line-height: 20.8px;
              letter-spacing: -0.011em;
            }

            &.suggest-item-active {
              cursor: default;

              .suggest-item__suggestion {
                background: var(--typo-colours-support-purple);
                border-radius: .25rem;
                color: #fff;
                padding: .25rem .5rem;
                transition-duration: .2s;
                cursor: pointer;
              }
            }
          }
        }

        .evaluation-essay-assessment {
          .essay-assessment__item-title {
            font-weight: 600;
          }
        }

        .evaluation-vocabulary {
          .vocabulary-item {
            line-height: 1.6875rem;

            .vocabulary-item__word {
              font-weight: 600;
            }
          }
        }

        .evaluation-criteria-assessment {
          .criteria-assessment__item-title {
            font-weight: 600;

            &::after {
              content: ' ';
            }
          }
        }

        .evaluation-improved-essay {
          p {
            white-space: pre-wrap;
            margin: 0;
          }
        }
      }

      .result-writing-output__action {
        border-top: 1px solid #C0BBD7;
        width: 100%;
        display: flex;
        flex-direction: row-reverse;
        padding-top: 8px;
        gap: 39px;

        >button {
          font-size: 14px;
          font-weight: 600;
          line-height: 20px;
          padding: 0;
        }
      }
    }
  }
}

