import { useTranslation } from "react-i18next";
import HtmlContent from "@component/HtmlContent";
import React from "react";
import { CONSTANT } from "@constant";
import { WRITING_EVALUATION_LIST } from "./functionCommon";

const GrammarStructureSuggestion = ({ grammarStructureSuggestion = {}, evaluationSelected }) => {
  const { t } = useTranslation();
  if (!grammarStructureSuggestion
    || !Object.keys(grammarStructureSuggestion)?.length
    || !['grammarStructureSuggestion', CONSTANT.ALL].includes(evaluationSelected)) return null;

  return <div className="evaluation__content__item evaluation-criteria-assessment">
    <div
      className="content__item__title evaluation-task-achievement__title"
      style={{ color: WRITING_EVALUATION_LIST.grammarStructureSuggestion.color }}
    >
      <img src={WRITING_EVALUATION_LIST.grammarStructureSuggestion.icon} alt="" />
      {t("GRAMMAR_STRUCTURE_SUGGESTION")}
    </div>
    <HtmlContent dangerouslySetInnerHTML={{ __html: grammarStructureSuggestion }} />
  </div>;
};

export default GrammarStructureSuggestion;