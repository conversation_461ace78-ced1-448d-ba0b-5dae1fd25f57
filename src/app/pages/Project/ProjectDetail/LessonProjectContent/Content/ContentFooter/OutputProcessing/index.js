import React from "react";
import <PERSON><PERSON> from "lottie-react";
import { useTranslation } from "react-i18next";

import { useProject } from "@app/pages/Project";
import { useContent } from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content";

import AntButton from "@component/AntButton";

import { BUTTON } from "@constant";

import SunIcon from "@component/SvgIcons/SunIcon";

import * as loadingAnimation from "@src/asset/animations/loading.json";

import "./OutputProcessing.scss";


function OutputProcessing(props) {
  const { t } = useTranslation();
  const { isAllowEditing, submitState } = useProject();
  
  return <>
    <div className="output-status">
      <div className="output-status__title">
        <SunIcon />
        <span>{t("HOLD_TIGHT!")}</span>
      </div>
      
      <Lottie animationData={loadingAnimation} loop={true} style={{ height: 150, width: 150 }} />
      
      {isAllowEditing && props.cancelResponseProcessing && <>
        <AntButton
          size="large"
          type={BUTTON.DEEP_NAVY}
          onClick={props.cancelResponseProcessing}
          disabled={submitState.isSubmit}
        >
          {t("CANCEL")}
        </AntButton>
      </>}
    </div>
  </>;
}

export default OutputProcessing;