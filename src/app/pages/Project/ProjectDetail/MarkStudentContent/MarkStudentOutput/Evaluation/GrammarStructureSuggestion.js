import { useTranslation } from "react-i18next";
import HtmlContent from "@component/HtmlContent";
import React from "react";

const GrammarStructureSuggestion = ({ grammarStructureSuggestion = {} }) => {
  const { t } = useTranslation();
  if (!Object.keys(grammarStructureSuggestion)?.length) return null;
  
  return <div className="evaluation__content__item evaluation-criteria-assessment">
    <div className="content__item__title evaluation-task-achievement__title">{t("GRAMMAR_STRUCTURE_SUGGESTION")}</div>
    <HtmlContent dangerouslySetInnerHTML={{ __html: grammarStructureSuggestion }}/>
  </div>;
};

export default GrammarStructureSuggestion;