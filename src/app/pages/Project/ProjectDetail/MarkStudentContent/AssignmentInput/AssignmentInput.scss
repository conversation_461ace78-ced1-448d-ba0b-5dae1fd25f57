@import "src/app/styles/scroll";

.exam-submission-container {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .exam-sub__header {
    flex-wrap: wrap;
    display: flex;
    column-gap: 32px;
    row-gap: 8px;

    .exam-sub__title {
      align-self: center;

      .exam-sub__primary-title {
        font-weight: 600;
      }

      .exam-sub__sub-title {

      }
    }

    .exam-sub__add {
      float: right;
      margin-left: auto;
    }
  }

  .exam-sub__list {
    .ant-table {
      table{
        max-width: 100%;
      }
      .ant-table-thead .ant-table-cell {
        padding: 14px 16px !important;
        font-weight: 400;
      }

      .ant-table-tbody .ant-table-row {
        .ant-table-cell {
          padding: 0;

          .exam-sub__cell {
            padding: 14px 16px;
            height: 76px;
            display: flex;
            flex-direction: column;
            justify-content: center;


            &:not(.exam-sub__cell-merge) {
              cursor: pointer;
            }

            .exam-sub__student-name {
              width: 280px;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }

            .exam-sub__student-score {
              width: 68px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
          }
        }

        &.row-selected {
          .ant-table-cell {
            background-color: var(--primary-colours-blue-navy-light-2);
          }
        }

        &:not(.row-selected) {
          .ant-table-cell.ant-table-cell-row-hover:not(:hover) {
            background-color: unset;
          }
        }
      }

      .exam-sub-list__header {
        display: flex;
        justify-content: space-between;
        gap: 16px;
        margin: -4px 0;

        .exam-sub-list__title {
          align-self: center;
        }

        .exam-sub-list__action {
          display: flex;
          gap: 8px;
        }
      }

      .ant-table-tbody .ant-table-row .ant-table-cell {
        &.exam-sub-item-info {
          .exam-sub__student-name {

          }

          .exam-sub__file-progress {
            margin-top: 4px;
            display: flex;
            gap: 8px;
            //height: 24px;

            .ant-progress {
              width: 240px;
            }

            .exam-sub__file-progress-status {
              height: 24px;
              //display: flex;
              align-items: center;
            }
          }
        }

        &.exam-sub-col {
          padding: 0 !important;
          background-color: unset !important;

          .exam-sub-detail {
            @extend .scrollbar;
            @extend .scrollbar-show;

            height: 385px;
            max-width: 960px;
            cursor: default;

            .exam-sub__img-default {
              display: flex;
              min-height: 100px;
              height: 100%;
              width: 100%;
              align-items: center;
              justify-content: center;

              img {
                height: 45px;
              }
            }
          }
        }
      }
    }

    .ant-pagination {
      margin-top: 8px;
      margin-bottom: 0;
    }
  }

  .mark-exam__submit {
    margin-top: 8px;
    display: flex;
    justify-content: center;
  }
}