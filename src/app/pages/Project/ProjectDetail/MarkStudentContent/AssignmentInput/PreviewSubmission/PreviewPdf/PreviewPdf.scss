.preview-pdf-container {
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  background: var(--lighttheme-content-background-stroke);

  &.preview-pdf-container__loading {
    background: none;
  }

  .preview-pdf-content {
    height: 100%;
    padding: 24px 50px;

    .react-pdf__Document {
      height: calc(100% - 28px);
      position: relative;

      .react-pdf__Page {
        height: 100%;

        >* {
          width: 100% !important;
          height: 100% !important;
        }
      }

      .preview-pdf__progress-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #000000B2;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 8px;

        .preview-pdf__progress {
          .ant-progress-text {
            color: var(--white)
          }

        }
      }
    }

    .preview-pdf__select-page {
      padding-top: 8px;
      text-align: center;
    }
  }

  &:not(:hover) {
    .preview-pdf-backdrop {
      display: none;
    }
  }

  .preview-pdf-backdrop {
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 10;

    .preview-icon {
      svg {
        width: 24px;
        height: 24px;
      }

      color: white;
    }
  }
}

.modal-preview-pdf-mask {
  background-color: #000000B2 !important;
}

.modal-preview-pdf-wraper {
  display: flex;

  .ant-modal {
    top: 0;
    margin: 50px auto;
    width: fit-content !important;
    padding-bottom: 0 !important;
    max-height: 100% !important;
    display: flex;
  }

  .modal-preview-pdf-content {
    background-color: unset !important;
    box-shadow: unset !important;
    border: none !important;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 !important;

    .modal-preview-pdf-body {
      height: 100%;
      width: fit-content;

      .preview-pdf-wrapper {
        height: 100%;

        .react-pdf__Document {
          height: calc(100% - 36px);
          aspect-ratio: 210/297;

          .react-pdf__Page {
            height: 100%;

            >* {
              width: 100% !important;
              height: 100% !important;
            }
          }
        }

        .preview-pdf__select-page {
          padding-top: 16px;
          justify-content: center;
          color: white;
          display: flex;
          gap: 16px;

          svg {
            path {
              stroke: var(--white) !important;
            }
          }
        }
      }
    }
  }
}