import React from "react";
import { useTranslation } from "react-i18next";

import HtmlContent from "@component/HtmlContent";
import TypingEffect from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/TypingEffect";
import Answer from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/Answer";
import Question from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/Question";


function HtmlQuestionOutputValue({ responseSelected }) {
  const { t } = useTranslation();
  
  const { output } = responseSelected || {};
  const { questionsHtml, answersHtml } = output || {};
  const showAnswer = !!answersHtml;
  
  const textHtml = (
    <div className="html-question-output-value">
      <Question />
      
      <HtmlContent dangerouslySetInnerHTML={{ __html: questionsHtml }} />
      
      {showAnswer && <Answer>
        <HtmlContent dangerouslySetInnerHTML={{ __html: answersHtml }} />
      </Answer>}
    </div>
  );
  
  if (responseSelected.typingEffect) return <TypingEffect html={textHtml} />;
  return textHtml;
}

export default HtmlQuestionOutputValue;