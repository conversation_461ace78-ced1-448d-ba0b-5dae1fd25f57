import React from "react";
import { useTranslation } from "react-i18next";


import TypingEffect from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/TypingEffect";
import OpenQuestion
  from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/OpenQuestionOutput/OpenQuestion";
import OpenQuestionAnswer
  from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/OpenQuestionOutput/OpenQuestionAnswer";
import Answer from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/Answer";


function OpenQuestionValue({ responseSelected, ...props }) {
  const { t } = useTranslation();
  
  const outputData = responseSelected.output;
  
  
  const trueFalseHtml = <div className="true-false-output">
    <div className="true-false-output__title">{t("QUESTION")}</div>
    
    <OpenQuestion options={outputData?.options} />
    
    <Answer>
      <OpenQuestionAnswer correctAnswers={outputData.correctAnswers} />
    </Answer>
  </div>;
  
  if (responseSelected.typingEffect)
    return <TypingEffect html={trueFalseHtml} />;
  return trueFalseHtml;
}

export default OpenQuestionValue;