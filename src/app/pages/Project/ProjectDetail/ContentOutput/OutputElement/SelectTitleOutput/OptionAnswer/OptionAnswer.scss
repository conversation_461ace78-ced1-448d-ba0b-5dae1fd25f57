.option-list-output__answer-list {
  display: flex;
  flex-direction: column;
  page-break-inside: auto;
  gap: 24px;

  .option-list-output__answer-item {
    display: flex;
    gap: 8px;

    .option-list-output__answer-index {
      display: flex;
      width: 24px;
      height: 24px;
      background: var(--typo-colours-support-blue-light);
      color: var(--white);
      border-radius: 50%;
      justify-content: center;
      line-height: 24px;
      margin-right: 8px;

    }

    .option-list-output__answer-text {
      flex: 1;
      align-self: center;
    }
  }
}