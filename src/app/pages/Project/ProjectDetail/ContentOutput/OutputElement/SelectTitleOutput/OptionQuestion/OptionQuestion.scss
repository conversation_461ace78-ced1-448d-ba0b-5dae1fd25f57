.option-list-output__value-container {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .option-list-output__question-item {
    display: flex;
    flex-direction: row;
    gap: 8px;


    .option-list-output__question-index {
      flex-shrink: 0;
      display: flex;
      width: 24px;
      height: 24px;
      background: var(--primary-colours-blue-navy);
      color: #FFFFFF;
      border-radius: 50%;
      justify-content: center;
      line-height: 24px;
    }

    .option-list-output__question-text {
      display: flex;
      line-height: 1.25;
      align-items: center;
    }
  }
}