import React, { useEffect, useState } from "react";
import { Button, Form, Input } from "antd";
import clsx from "clsx";

import { useProject } from "@app/pages/Project";

import { CONSTANT } from "@constant";
import { cloneObj } from "@common/functionCommons";
import { handleMoveOutput } from "../../outputCommons";

import ArrowUp from "@component/SvgIcons/ArrowUp";
import ArrowDown from "@component/SvgIcons/ArrowDown";
import TimeIcon from "@component/SvgIcons/TimeIcon";
import PlusIcon from "@component/SvgIcons/PlusIcon";


function SelectTitleForm({ responseSelected, isEditing, setEditing, ...props }) {
  const { handleSubmitResponse } = useProject();
  const { content, outputForm } = props;
  
  const [optionData, setOptionData] = useState([]);
  const [correctOption, setCorrectOption] = useState(undefined);
  
  useEffect(() => {
    
    if (Array.isArray(responseSelected?.output?.options)) {
      setOptionData(responseSelected.output.options);
    }
    setCorrectOption(responseSelected?.output?.correctOptionId);
  }, [responseSelected]);
  
  function moveOption(optionId, type) {
    const correctOptionText = optionData.find(option => option.optionId === correctOption)?.text;
    const optionNew = handleMoveOutput(optionData, optionId, type, "optionId");
    setOptionData(optionNew);
    setCorrectOption(optionNew.find(state => state.text === correctOptionText)?.optionId);
  }
  
  function handleChangeOption(optionId, value) {
    setOptionData(prevState => {
      const newState = cloneObj(prevState);
      return newState.map(state => {
        if (state.optionId === optionId) {
          state.text = value;
        }
        return state;
      });
    });
  }
  
  function handleDeleteOption(optionId) {
    setOptionData(prevState => {
      return cloneObj(prevState)
        .filter(state => state.optionId !== optionId)
        .map((state, index) => {
          state.optionId = index + 1;
          return state;
        });
    });
    
    if (optionId < correctOption) {
      setCorrectOption(prevState => prevState - 1);
    } else if (optionId === correctOption) {
      setCorrectOption(undefined);
    }
    
  }
  
  function handleAddOption() {
    setOptionData(prevState => [...prevState, { optionId: prevState.length + 1 }]);
  }
  
  
  function onFinishResponse() {
    const output = { options: optionData, correctOptionId: correctOption };
    handleSubmitResponse(content._id, { _id: responseSelected._id, output })
      .then(() => setEditing(false));
  }
  
  return <div className="option-list-output">
    <div className="option-list-output__form-container">
      <Form
        id={`form-response-${responseSelected._id}`}
        className="hidden"
        form={outputForm}
        onFinish={onFinishResponse}
      />
      
      {optionData.map((optionItem, index) => {
        const isLastItem = index === optionData.length - 1;
        return <div
          key={optionItem.optionId}
          className="option-list-output__form-item"
        >
          <div className="option-list-output__form-action">
            {!!index && <Button
              icon={<ArrowUp />} className="option-list-output__action-up"
              onClick={() => moveOption(optionItem.optionId, CONSTANT.MOVE_UP)}
            />}
            {(optionData.length === 1 || !isLastItem) && <Button
              icon={<ArrowDown />} className="option-list-output__action-down"
              onClick={() => moveOption(optionItem.optionId, CONSTANT.MOVE_DOWN)}
              disabled={isLastItem}
            />}
          </div>
          
          <div className="option-list-output__form-content">
            <div
              //className="option-list-content__input"
              className={clsx("option-list-content__input", { "option-list-content__correct-option": correctOption === optionItem.optionId })}
            >
              <Input
                value={optionItem.text}
                onChange={(e) => handleChangeOption(optionItem.optionId, e.target.value)}
                prefix={<div
                  className="option-list-content-item__radio"
                  onClick={() => setCorrectOption(optionItem.optionId)}
                >
                  {optionItem.optionId}.
                </div>}
              />
            </div>
            <div className="option-list-content__action">
              <Button
                size="small" shape="circle" icon={<TimeIcon />}
                onClick={() => handleDeleteOption(optionItem.optionId)}
              />
            </div>
          </div>
        </div>;
      })}
      
      <div className="project-content-output__add">
        <Button
          className="btn-add-question"
          shape="circle" icon={<PlusIcon />}
          onClick={handleAddOption}
        />
      </div>
    
    </div>
  </div>;
}

export default SelectTitleForm;