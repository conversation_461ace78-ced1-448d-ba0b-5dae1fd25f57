import React from "react";
import { useTranslation } from "react-i18next";

import TypingEffect from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/TypingEffect";
import Question from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/Question";
import Answer from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/Answer";
import AbcdQuestion from "./AbcdQuestion";
import AbcdAnswer from "./AbcdAnswer";

function AbcdValue({ responseSelected, ...props }) {
  const { t } = useTranslation();
  const outputData = responseSelected.output;
  
  const abcdHtml = <div className="abcd-output">
    <Question />
    
    <AbcdQuestion questions={outputData?.questions} />
    
    <Answer>
      <AbcdAnswer correctAnswers={outputData?.correctAnswers} />
    </Answer>
  </div>;
  
  return <>
    {responseSelected.typingEffect
      ? <TypingEffect html={abcdHtml} />
      : abcdHtml}
  </>;
}

export default AbcdValue;