import React, { useEffect } from "react";
import { InputNumber  } from "antd";
import { useTranslation } from "react-i18next";

import { useProject } from "@app/pages/Project";

import CustomCKEditor from "@component/CKEditor";
import { AntForm } from "@component/AntForm";

import RULE from "@rule";

function MarkTestWritingOutputForm({ responseSelected, isEditing, setEditing, ...props }) {
  const { t } = useTranslation();

  const { handleSubmitResponse } = useProject();

  const { content, outputForm } = props;

  useEffect(() => {
    setFormData();
  }, [responseSelected]);

  useEffect(() => {
    if (!isEditing) {
      setFormData();
    }
  }, [isEditing]);

  function setFormData() {
    if (responseSelected?.output) {
      outputForm.setFieldsValue({
        evaluation: responseSelected.output.evaluation || "",
        score: responseSelected.output.score || "",
      });
    } else {
      outputForm.resetFields();
    }
  }

  function onFinishResponse(values) {
    const outputRequest = {
      ...responseSelected.output,
      evaluation: values.evaluation || "",
      score: values.score || "",
    };
    handleSubmitResponse(content._id, { _id: responseSelected._id, output: outputRequest })
      .then(() => setEditing(false));
  }

  return <AntForm
    layout="vertical"
    id={`form-response-${responseSelected._id}`}
    form={outputForm}
    onFinish={onFinishResponse}
    className="mark-test-writing-form"
  >
    <AntForm.Item
      name="evaluation"
      rules={[RULE.REQUIRED]}
    >
      <CustomCKEditor />
    </AntForm.Item>
    <AntForm.Item
      label={t("SCORE")}
      name="score"
      rules={[{ required: true, message: t("CAN_NOT_BE_BLANK") }]}
    >
      <InputNumber size="large" controls={false} min={0} max={10}/>
    </AntForm.Item>
  </AntForm>;
}

export default MarkTestWritingOutputForm;