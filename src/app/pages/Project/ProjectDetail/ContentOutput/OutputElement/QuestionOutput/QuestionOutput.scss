//:root {
//  --warm-up-checkbox: url('../../../../../../asset/icon/button/radio-light.svg');
//  --warm-up-checkbox-selected: url('../../../../../../asset/icon/button/radio-selected-light.svg');
//}
//
//[data-theme='dark'] {
//  --warm-up-checkbox: url('../../../../../../asset/icon/button/radio-light.svg');
//  --warm-up-checkbox-selected: url('../../../../../../asset/icon/button/radio-selected-light.svg');
//}

.option-list-output {
  display: flex;
  flex-direction: column;
  gap: 24px;



  .option-list-output__form-container {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .option-list-output__form-item {

      display: flex;
      flex-direction: row;


      .option-list-output__form-action {
        border-radius: 16px 0 0 16px;
        border: 1px solid var(--lighttheme-content-background-stroke);
        display: flex;
        padding: 4px 11px;
        flex-direction: column;
        justify-content: center;
        gap: 16px;
      }

      .option-list-output__form-content {
        display: flex;
        flex-direction: row;
        gap: 8px;

        border-radius: 0 16px 16px 0;
        border: 1px solid var(--lighttheme-content-background-stroke);
        background: #FFF;
        padding: 24px;
        //width: calc(100% - 55px);
        margin-left: -1px;
        flex: 1;

        .option-list-content__input {
          //width: calc(100% - 32px);
          flex: 1;

          .ant-input-affix-wrapper {
            border-radius: 4px;
            border: 1px solid var(--lighttheme-content-background-stroke);
            padding-left: 10px;
            padding-right: 10px;
          }

          .ant-input-prefix {
            margin-inline-end: 5.5px;
            cursor: pointer;

            .option-list-content-item__radio {
              height: 20px;
              background-image: var(--abcd-checkbox);
              background-repeat: no-repeat;
              padding-left: 24px;
              background-position: left;
            }
          }

          .ant-input {
            line-height: 20px; /* 125% */
          }


          &.option-list-content__correct-option {
            //background: red;

            .ant-input-affix-wrapper {
              border-color: var(--primary-colours-blue);
              background: var(--primary-colours-blue-light-2);

              .ant-input-prefix .option-list-content-item__radio {
                color: var(--primary-colours-blue);
                background-image: var(--abcd-checkbox-selected);
              }

              .ant-input {
                background: var(--primary-colours-blue-light-2);
                color: var(--primary-colours-blue);
              }
            }
          }

        }


        .option-list-content__action {

          display: flex;
          align-items: center;

          .ant-btn {
            box-shadow: var(--shadow-level-2);

            .ant-btn-icon {
              display: flex;
              place-content: center;
            }
          }
        }
      }


    }

  }
}
