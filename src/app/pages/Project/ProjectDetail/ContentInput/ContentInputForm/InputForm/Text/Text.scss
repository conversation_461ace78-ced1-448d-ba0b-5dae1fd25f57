:root {
  --form-text-input-help-color: #858585;
  --form-text-input-help-link-color: var(--primary-colours-blue);
}

.upload-buttons {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;

  button {
    display: flex;
    padding: 6px 16px;
    align-items: center;
    border-radius: 4px;
    font-weight: 400;
  }

  .upload-buttons__image {
    background: rgba(15, 208, 173, 0.10);
    color: #0FD0AD;
  }

  .upload-buttons__file {
    background: rgba(193, 15, 208, 0.10);
    color: #C10FD0;
  }
}

.document-preview {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .document-preview__label {
    font-weight: 600;
  }

  .document-preview__document-name {
    display: flex;
    padding: 9px 15px;
    align-items: center;
    border-radius: 4px;
    border: 1px solid var(--lighttheme-content-background-stroke);
    width: 100%;
    word-break: break-all;
  }

  .document-preview__image {
    position: relative;
    display: flex;
    width: fit-content;

    img {
      border-radius: 4px;
      border: 1px solid var(--background-light-background-grey) !important;
      object-fit: contain;
    }
  }
}

.form-text-input {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .ant-form-item {
    margin-bottom: 0;

    .ant-form-item-explain-error {
      margin-bottom: 0 !important;
    }
  }

  .voice-option-container {
    display: flex;
    flex-direction: row;
    gap: 16px;
    flex-wrap: wrap;

    .option-voice-btn {
      padding: 0 8px;
      min-width: 153px;
      justify-content: unset;
      gap: 8px;
      font-weight: 400;

      .option-voice-avatar {
        display: flex;
        align-items: center;

        img {
          height: 32px;
          width: 32px;
        }
      }

      .ant-btn-icon {
        margin-left: auto;

        svg {
          height: 24px;
          width: 24px;
        }
      }
    }
  }

  .voice-speed-container {
    display: flex;
    flex-direction: row;
    gap: 16px;
    flex-wrap: wrap;

    .option-speed-btn {
      padding: 10px 16px;
      gap: 16px;
      font-weight: 400;

      .option-speed-text{
        font-weight: 600;
      }
    }
  }
}