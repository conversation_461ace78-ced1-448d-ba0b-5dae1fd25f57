.image-from-resource-container {
  .image-from-resource__list {
    display: grid;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    gap: 24px;
    max-height: calc(100vh - 490px);
    overflow-y: auto;

    @media screen and (max-height: 720px) {
      max-height: calc(100vh - 330px);
      column-gap: 24px;
      row-gap: 16px;
    }

    .image-from-resource__item {

      padding: 15px;
      border: 1px solid transparent;
      box-shadow: var(--shadow-level-3);
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      cursor: pointer;
      background-color: var(--white);

      &.image-from-resource__item-active {
        border-color: var(--primary-colours-blue);
      }

      .image-from-resource__item-preview {
        aspect-ratio: 1/1;
        display: flex;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .image-from-resource__item-title {

      }

      .image-from-resource__item-update-at{
        margin-top: -4px;
        font-size: 10px;
        color: var(--typo-colours-support-blue-light);
        line-height: 20px;
      }
    }
  }
}