import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";

import AntButton from "@component/AntButton";
import AntModal from "@component/AntModal";
import UploadFromComputer from "./UploadFromComputer";
import SelectAudioFromResource from "./SelectAudioFromResource";
import ResourceCategories from "@component/Resource/ResourceCategories";

import { BUTTON, CONSTANT } from "@constant";

import "./UploadAudio.scss";


function SelectAudio({ user, isOpen, ...props }) {
  const { t } = useTranslation();
  
  const [categorySelected, setCategorySelected] = useState(CONSTANT.MY_COMPUTER);
  
  useEffect(() => {
    if (!isOpen) {
      setCategorySelected(CONSTANT.MY_COMPUTER);
    }
  }, [isOpen]);
  
  return <AntModal
    width={1056}
    title={t("UPLOAD_AUDIO")}
    description={t("UPLOAD_AUDIO_DESCRIPTION")}
    open={isOpen}
    onCancel={props.handleCancel}
    footerless
  >
    <div className="upload-audio-modal">
      <ResourceCategories
        allowUpload={true}
        categorySelected={categorySelected}
        setCategorySelected={setCategorySelected}
      />
      
      <UploadFromComputer categorySelected={categorySelected} />
      
      <SelectAudioFromResource categorySelected={categorySelected} />
    </div>
  
  </AntModal>;
}


function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(UploadAudio);