import React from "react";
import { useProject } from "@app/pages/Project";

import { ContentInputForm } from "./ContentInputForm";

import "./Input.scss";


function ContentInput(props) {
  const { setSubmitState } = useProject();
  const { content, isEditing } = props;
  
  
  async function onFinish(values) {
    if (isEditing) return;
    
    setSubmitState({
      isSubmit: true,
      submitValue: values,
      contentId: content._id,
    });
  }
  
  return <ContentInputForm
    {...props}
    onFinish={onFinish}
  />;
}

export default ContentInput;
