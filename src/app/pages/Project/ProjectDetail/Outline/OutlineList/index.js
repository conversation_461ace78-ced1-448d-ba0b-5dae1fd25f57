import React, { useEffect, useRef, useState } from "react";
import { connect } from "react-redux";
import { ReactSortable } from "react-sortablejs";
import { useTranslation } from "react-i18next";

import { useProject } from "@app/pages/Project";

import { CONSTANT } from "@constant";
import { cloneObj } from "@common/functionCommons";
import { moveContentToIndex } from "@services/Content";

import "./OutlineList.scss";
import { extractKeys } from "@common/dataConverter";


function OutlineList({ ...props }) {
  const { t, i18n } = useTranslation();
  
  const {
    isPreviewProject,
    isExam, examOutputData, examOrderSelected,
    projectContentData, setProjectContentData,
    isAllowEditing,
    scrollToContent,
  } = useProject();
  
  const isPreview = useRef(false);
  const sortData = useRef([]);
  const outlineActive = useRef(undefined);
  
  const [outlineData, setOutlineData] = useState([]);
  
  useEffect(() => {
    isPreview.current = isPreviewProject;
    
    const contentData = cloneObj(projectContentData);
    if (!isExam) {
      setOutlineData(isPreviewProject
        ? contentData.filter(content => {
          if (!content.responses?.length || content.isHidden) return false;
          return content.responses?.find(response => response.isActivate)?.state === CONSTANT.DONE.toLowerCase();
        })
        : contentData);
    } else {
      const outputExamSelected = examOutputData[examOrderSelected];
      
      const outputExamContentId = extractKeys(outputExamSelected, "contentId");
      
      if (outputExamContentId?.length) {
        const contentOutline = contentData.filter(content => outputExamContentId.includes(content._id));
        setOutlineData(contentOutline);
      } else {
        setOutlineData([]);
      }
    }
    
    if (outlineActive.current) {
      changeActive(outlineActive.current);
      outlineActive.current = undefined;
    }
    
  }, [projectContentData, isPreviewProject, examOutputData, examOrderSelected]);
  
  useEffect(() => {
    const jsContent = document.getElementById("js-layout-content");
    jsContent.addEventListener("scroll", activeAnchor);
    return () => {
      jsContent.removeEventListener("scroll", activeAnchor);
    };
  }, []);
  
  useEffect(() => {
    activeAnchor();
  }, [outlineData]);
  
  function getVisibleArea(el) {
    const rect = el.getBoundingClientRect();
    const windowHeight = (window.innerHeight || document.documentElement.clientHeight);
    const windowWidth = (window.innerWidth || document.documentElement.clientWidth);
    
    // Tính toán tọa độ của phần tử nằm trong khung nhìn
    const visibleWidth = Math.max(0, Math.min(rect.right, windowWidth) - Math.max(rect.left, 0));
    const visibleHeight = Math.max(0, Math.min(rect.bottom, windowHeight) - Math.max(rect.top, 0));
    return visibleWidth * visibleHeight;
  }
  
  function findElementWithMostVisibleArea(elements) {
    let maxVisibleArea = 0;
    let elementWithMostVisibleArea = null;
    elements.forEach(el => {
      const visibleArea = getVisibleArea(el.contentEl);
      if (visibleArea > maxVisibleArea) {
        maxVisibleArea = visibleArea;
        elementWithMostVisibleArea = el;
      }
    });
    
    return elementWithMostVisibleArea;
  }
  
  
  function activeAnchor() {
    const jsProjectContent = document.getElementById("js-project-content");
    
    const projectContent = isPreview.current
      ? jsProjectContent.querySelectorAll(".preview-project__item")
      : jsProjectContent.querySelectorAll(isExam ? ".exam-output-content" : ".project-content-layout");
    
    const elementList = [...projectContent].map((content, index) => {
      const id = isPreview.current
        ? content.getAttribute("id")?.replace("js-project-preview-", "")
        : content.getAttribute("id")?.replace("js-project-content-", "");
      return {
        id,
        contentEl: content,
        outlineEl: document.querySelectorAll(`[data-id="${id}"]`)?.[0],
      };
    });
    
    const mostVisibleElement = findElementWithMostVisibleArea(elementList);
    elementList.forEach(el => {
      if (el.id === mostVisibleElement?.id) {
        el.outlineEl?.classList.add("outline__item-active");
      } else {
        el.outlineEl?.classList.remove("outline__item-active");
      }
    });
    
  }
  
  function onStartSort() {
    if (!isAllowEditing) return;
    sortData.current = outlineData;
  }
  
  async function onEndSort(evt) {
    if (!sortData.current || !isAllowEditing) return;
    
    const contentId = sortData.current.find(content => content.contentIndex === evt.newIndex + 1)?._id;
    outlineActive.current = contentId;
    const sortDataObj = sortData.current.reduce(function (grouped, element) {
      const { contentIndex, title } = element;
      grouped[element._id] = { contentIndex, title };
      return grouped;
    }, {});
    
    await setProjectContentData(prevState => {
      const newState = cloneObj(prevState);
      newState.forEach(state => {
        state.contentIndex = sortDataObj[state._id].contentIndex;
      });
      return newState.sort((a, b) => a.contentIndex - b.contentIndex);
    });
    
    moveContentToIndex({ _id: contentId, newContentIndex: evt.newIndex + 1 });
  }
  
  function changeActive(contentId) {
    scrollToContent(contentId);
  }
  
  function onUnchoose(evt) {
    const contentScrollTo = evt?.item?.getAttribute("data-id");
    scrollToContent(contentScrollTo);
  }
  
  function reIndex(values) {
    if (!isAllowEditing) return;
    sortData.current = values.map((value, index) => {
      value.contentIndex = index + 1;
      return value;
    });
  }
  
  return <div id="js-outline" className="outline-container">
    <div className="outline__title">
      {t("TABLE_OF_CONTENT")}
    </div>
    <div className="outline__list">
      <ReactSortable
        id="js-outline-list"
        ghostClass="outline__item-ghost"
        chosenClass="outline__item-chosen"
        list={outlineData}
        setList={reIndex}
        onStart={onStartSort}
        onEnd={onEndSort}
        onUnchoose={onUnchoose}
        //disabled={!isAllowEditing}
      >
        {outlineData?.map((content, index) => {
          return <div
            key={content._id}
            data-id={content._id}
            className="outline__item"
          >
            <div className="outline-item__icon" />
            <div className="outline-item__text">
              {`${content.contentIndex}. ${content.title}`}
            </div>
          </div>;
        })}
      </ReactSortable>
    </div>
  </div>;
}


function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(OutlineList);