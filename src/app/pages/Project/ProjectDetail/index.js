import React, { useEffect, useMemo, useState } from "react";
import { Dropdown } from "antd";
import { useTranslation } from "react-i18next";
import { useProject } from "@app/pages/Project";
import clsx from "clsx";

import { usePageViewTracker } from "@src/ga";

import AntButton from "@component/AntButton";
import ProjectInfo from "@app/pages/Project/ProjectDetail/ProjectInfo";
import Outline from "@app/pages/Project/ProjectDetail/Outline";
import LessonProjectContent from "@app/pages/Project/ProjectDetail/LessonProjectContent";
import ExamProjectContent from "@app/pages/Project/ProjectDetail/ExamProjectContent";
import SegmentedPreview from "@app/pages/Project/ProjectDetail/SegmentedPreview";
import TemplateAndTool from "@app/pages/Project/TemplateAndTool";
import StartProject from "@app/pages/Project/StartProject";
import PreviewProject from "@app/pages/Project/PreviewProject";
import ViewMode from "@app/pages/Project/ProjectDetail/ViewMode";
import { MarkExamContent } from "@app/pages/Project/ProjectDetail/MarkExamContent";

import { CONSTANT, PROJECT_TYPE, TYPE_OF_TOOL } from "@constant";
import { cloneObj } from "@common/functionCommons";
import { submitInputData } from "@services/Content";

import ChevronDown from "@component/SvgIcons/ChevronDown";

import "./ProjectDetail.scss";
import { MarkStudentContent } from "@app/pages/Project/ProjectDetail/MarkStudentContent";
import MarkSpeaking from "@app/pages/Project/ProjectDetail/MarkSpeaking";


function ProjectDetail(props) {
  usePageViewTracker("ProjectDetail");
  const { t } = useTranslation();
  const { projectData, isShowInfoMobile, isShowOutlineMobile, projectContentData } = useProject();
  const { submitState, setSubmitState, setProjectContentData } = useProject();
  const { contentDownload, isPreviewProject, isShowPlainText } = useProject();
  const { isExam, isMark, isMarkStudent, isMarkSpeaking } = useProject();
  const [isOpenInfoDropdown, setOpenInfoDropdown] = useState(false);
  
  useEffect(() => {
    if (submitState.isSubmit) {
      setTimeout(() => {
        submitInput();
      }, 500);
    }
  }, [submitState.isSubmit]);
  
  async function submitInput() {
    const params = { workspaceId: projectData?.workspaceId };
    const apiResponse = await submitInputData(submitState.contentId, submitState.submitValue, false, params);
    if (apiResponse) {
      setProjectContentData(prevState => {
        const newState = cloneObj(prevState);
        newState.forEach(existContent => {
          if (existContent._id === submitState.contentId) {
            existContent.inputs ||= [];
            existContent.inputs.push(apiResponse.inputId);
            //existContent.lastInput = apiResponse.inputId;
            existContent.responses ||= [];
            existContent.responses.forEach(response => {
              response.isActivate = false;
            });
            existContent.responses.push({ ...apiResponse, isActivate: true, typingEffect: true });
          }
        });
        return newState;
      });
    }
    setSubmitState({ isSubmit: false, submitValue: null, contentId: null });
  }
  
  
  function onOpenChange(open, { source }) {
    if (source === "trigger") setOpenInfoDropdown(open);
  }
  
  const renderContent = () => {
    switch (true) {
      case isMarkStudent:
        return <MarkStudentContent />;
      case isMarkSpeaking:
        return <MarkSpeaking />;
      case isExam:
        return <ExamProjectContent />;
      case isMark:
        return <MarkExamContent />;
      default:
        return <LessonProjectContent />;
    }
  };
  
  return <div className={clsx("project-detail", { "project-detail-preview": isPreviewProject && !isMarkSpeaking })}>
    
    <Outline />
    
    <div id="js-project-content" className="project-content">
      {isShowInfoMobile && <div id="js-project-info-mobile" className="project-info__mobile">
        {isShowOutlineMobile && <SegmentedPreview />}
        
        <Dropdown
          trigger="click"
          onOpenChange={onOpenChange}
          open={isOpenInfoDropdown}
          overlayClassName="project-info__mobile-dropdown"
          menu={{ items: [{ key: 1, label: <ProjectInfo affixTargetId="js-project-info-mobile" /> }] }}
        >
          <div className="project-info__dropdown-button">
            <AntButton
              size="small"
              iconLocation={CONSTANT.RIGHT}
              icon={<ChevronDown />}
            >
              {t("PROJECT")}
            </AntButton>
          </div>
        </Dropdown>
      </div>}
      
      <StartProject />
      
      {isPreviewProject && <ViewMode />}
      
      {/* {isExam
       ? <ExamProjectContent />
       : isMark
       ? <MarkExamContent />
       : <LessonProjectContent />} */}
      
      {renderContent()}
      
      <PreviewProject
        onlyPlaintext={isShowPlainText}
        allowAction={true}
        showDivider={true}
        contentPreview={contentDownload}
      />
      
      <TemplateAndTool />
    
    </div>
    
    {!isShowInfoMobile && !isMarkStudent && <ProjectInfo />}
  </div>;
}

export default ProjectDetail;