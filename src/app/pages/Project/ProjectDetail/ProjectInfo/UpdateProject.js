import React, { useEffect } from "react";
import { Form, Input } from "antd";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";

import { toast } from "@component/ToastProvider";
import { useProject } from "@app/pages/Project";

import AntModal from "@component/AntModal";

import { CONSTANT } from "@constant";
import { updateProject } from "@services/Project";

import * as app from "@src/ducks/app.duck";

function UpdateProject({ open, onClose, updateType, ...props }) {
  const { t } = useTranslation();
  
  const [formUpdateProject] = Form.useForm();
  const { projectId, projectData, setProjectData } = useProject();
  
  useEffect(() => {
    formUpdateProject.resetFields();
    if (open) {
      formUpdateProject.setFieldsValue(projectData);
    }
  }, [open]);
  
  async function onFinish(values) {
    const { projectName, description } = values;
    const apiRequest = { _id: projectId, projectName, description };
    const params = {
      workspaceId: projectData?.workspaceId?._id || projectData?.workspaceId,
    }
    const dataResponse = await updateProject(apiRequest, true, params);
    if (dataResponse) {
      setProjectData(prevState => Object.assign({}, prevState, dataResponse));
      toast.success("UPDATE_PROJECT_SUCCESS");
      props.onCancel();
      props.setProjectBreadcrumb(dataResponse);
    }
  }
  
  return <>
    <AntModal
      formId="form-update-project"
      open={open}
      closeIcon={null}
      {...props}
    >
      <Form
        id="form-update-project"
        layout="vertical"
        form={formUpdateProject}
        onFinish={onFinish}
        requiredMark={false}
      >
        {updateType === CONSTANT.NAME && <Form.Item
          name="projectName"
          label={t("PROJECT_NAME")}
          rules={[
            () => ({
              validator(_, value) {
                if (value.trim()) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
              },
            }),
          ]}
        >
          <Input size="large" />
        </Form.Item>}
        
        {updateType === CONSTANT.DESCRIPTION && <Form.Item
          name="description"
          label={t("PROJECT_DESCRIPTION")}
          rules={[
            () => ({
              validator(_, value) {
                if (value.trim()) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
              },
            }),
          ]}
        >
          <Input size="large" />
        </Form.Item>}
      </Form>
    </AntModal>
  </>;
}

function mapStateToProps(store) {
  return {};
}

const mapDispatchToProps = { ...app.actions };

export default connect(mapStateToProps, mapDispatchToProps)(UpdateProject);