import React, { useEffect, useMemo, useState } from "react";
import { Form, Input, Popover } from "antd";
import { useTranslation } from "react-i18next";

import { useProject } from "@app/pages/Project";
import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";

import ActionPopover from "@app/pages/Project/ProjectDetail/ActionPopover";

;
import { DownloadProject } from "@app/pages/Project/DownloadProject";

import { BUTTON, CONSTANT } from "@constant";
import { cloneObj } from "@common/functionCommons";
import { populateTool } from "@app/pages/Project/ProjectDetail/projectCommons";
import { retryResponse } from "@services/Response";

import Copy from "@component/SvgIcons/Copy";
import Edit from "@component/SvgIcons/Edit";
import Download from "@component/SvgIcons/Download";
import Rotate from "@component/SvgIcons/Rotate";

import "./ExamOutputAction.scss";
import RegenerateExam
  from "@app/pages/Project/ProjectDetail/ExamProjectContent/ExamProjectOutput/ExamOutputItem/RegenerateExam";

function ExamOutputAction({ output, contentData, outputForm, isEditing, setEditing, toolInfo, ...props }) {
  const { t } = useTranslation();
  
  const { isAllowEditing } = useProject();
  
  const [isShowDownload, setShowDownload] = useState(false);
  
  const [toolData, instructionSelected] = useMemo(() => {
    const instructionId = output?.inputId?.inputData?.instructionId;
    const tool = populateTool(output?.toolId);
    const instruction = tool?.instructionIds?.find(instructionItem => instructionItem._id === instructionId);
    return [tool, instruction];
  }, [output]);
  
  const [isCopied, setCopied] = useState(false);
  
  useEffect(() => {
    if (isCopied) {
      setTimeout(() => {
        setCopied(false);
      }, 1000);
    }
  }, [isCopied]);
  
  function copyOutput() {
    if (!isCopied && output.plaintext) {
      navigator.clipboard.writeText(output.plaintext);
      setCopied(true);
    }
  }
  
  function handleEditResponse() {
    setEditing(true);
  }
  
  
  const disabledAction = useMemo(() => {
    return output?.state?.toUpperCase() === CONSTANT.PROCESSING;
  }, [output?.state]);
  
  if (isEditing) {
    return <div className="exam-output-actions exam-output-actions-editing">
      <AntButton size="large" onClick={() => setEditing(false)}>
        {t("CANCEL")}
      </AntButton>
      
      <AntButton
        size="large"
        type={BUTTON.DEEP_NAVY}
        onClick={() => outputForm.submit()}
      >
        {t("SAVE")}
      </AntButton>
    </div>;
  }
  
  return <>
    <div className="exam-output-actions">
      {instructionSelected?.showAdditionalRequest && !toolData.isDisable && isAllowEditing && <>
        
        <RegenerateExam
          output={output}
          contentData={contentData}
          insInit={instructionSelected}
          toolInfo={toolInfo}
          disabled={disabledAction}
        />
      </>}
      
      <ActionPopover
        content="COPY"
        icon={<Copy />}
        onClick={copyOutput}
        disabled={disabledAction}
      />
      
      {isAllowEditing &&
        <ActionPopover
          content="EDIT"
          icon={<Edit />}
          onClick={handleEditResponse}
          disabled={disabledAction}
        />}
      
      <ActionPopover
        content="DOWNLOAD"
        icon={<Download />}
        onClick={() => setShowDownload(true)}
        disabled={disabledAction}
      />
    </div>
    
    <DownloadProject
      open={isShowDownload}
      onCancel={() => setShowDownload(false)}
      downloadType={CONSTANT.CONTENT}
      contentIdInitial={output.contentId}
    />
  </>;
}

export default ExamOutputAction;