import React, { useEffect, useMemo, useState } from "react";
import { connect } from "react-redux";
import { Collapse } from "antd";
import { useTranslation } from "react-i18next";
import _ from "lodash";

import { useProject } from "@app/pages/Project";

import AntButton from "@component/AntButton";
import ContentInput from "@app/pages/Project/ProjectDetail/ContentInput";
import ContentHeader from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content/ContentHeader";
import ContentActions from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content/ContentActions";

import { BUTTON, CONSTANT } from "@constant";
import { getToolInfo } from "@app/pages/Project/ProjectDetail/projectCommons";

import Plus from "@component/SvgIcons/Plus";

import "./ExamInputItem.scss";

function ExamInputItem({ content }) {
  const { t } = useTranslation();
  
  const { isAllowEditing, examOutputData, totalExam, examOrderSelected } = useProject();
  const { setStateMoreTool } = useProject();
  
  const [isMinimize, setMinimize] = useState(false);
  
  const [isFetchingContent, setFetchingContent] = useState(false);
  const [instructionSelected, setInstructionSelected] = useState(undefined);
  
  
  const toolInfo = useMemo(() => getToolInfo(content), [content]);
  
  
  const isDisabledContent = useMemo(() => !isAllowEditing || toolInfo?.isDisable, [isAllowEditing, toolInfo]);
  
  const inputData = useMemo(() => {
    return examOutputData[examOrderSelected]?.find(output => output.contentId === content._id)?.inputId?.inputData;
  }, [examOutputData, examOrderSelected]);
  
  const [isEditing, setEditing] = useState(false);
  
  async function doMagic(values) {
    // do nothing
  }
  
  return <div className="exam-input-item">
    
    <Collapse
      activeKey={isMinimize ? [] : [CONSTANT.INPUT]}
      ghost={true}
      className=""
      items={[{
        key: CONSTANT.INPUT,
        showArrow: false,
        label: <ContentHeader
          contentIndex={content.contentIndex}
          toolInfo={toolInfo}
          isMinimize={isMinimize}
          showDesc={false}
          actions={<ContentActions
            iconOnly={true}
            content={content}
            toolInfo={toolInfo}
            isMinimize={isMinimize}
            setMinimize={setMinimize}
          />}
        />,
        children: <ContentInput
          content={content}
          toolInfo={toolInfo}
          doMagic={doMagic}
          isEditing={isEditing}
          inputData={inputData}
          isDisabledContent={isDisabledContent}
          setFetchingContent={setFetchingContent}
          instructionSelected={instructionSelected}
          setInstructionSelected={setInstructionSelected}
        />,
      }]}
    />
    <div className="exam-input__add-tool">
      <AntButton
        size="large"
        type={BUTTON.LIGHT_GREEN}
        icon={<Plus />}
        iconLocation={CONSTANT.RIGHT}
        onClick={() => setStateMoreTool({ isShowModal: true, contentIndex: content.contentIndex })}
      >
        {t("ADD_TOOL")}
      </AntButton>
    </div>
  </div>;
}


function mapStateToProps(store) {
  return {};
}

export default connect(mapStateToProps)(ExamInputItem);